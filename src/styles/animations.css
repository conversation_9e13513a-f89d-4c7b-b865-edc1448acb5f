/* Minimal and Professional Animations for BPMAX */

/* Very subtle floating animation for the main SVG */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* SVG Shadow that follows the shape */
.svg-shadow {
  filter: drop-shadow(0 10px 25px rgba(0, 0, 0, 0.1))
          drop-shadow(0 4px 10px rgba(0, 0, 0, 0.05));
  transition: filter 0.3s ease;
}

.svg-shadow:hover {
  filter: drop-shadow(0 15px 35px rgba(0, 0, 0, 0.15))
          drop-shadow(0 6px 15px rgba(0, 0, 0, 0.08));
}
