import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Phone,
  Mail,
  MapPin,
  Clock,
  Users,
  MessageSquare,
  CheckCircle,
  ArrowRight,
  Building2,
  Calendar,
  Globe,
  Linkedin,
  Twitter,
  Youtube
} from 'lucide-react';

const ContactPage = () => {
  const { t } = useTranslation();

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    jobTitle: '',
    industry: '',
    phone: '',
    country: '',
    inquiryType: '',
    message: '',
    newsletter: false,
    terms: false
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));

    setIsSubmitting(false);
    setIsSubmitted(true);
  };

  const contactMethodsData = t('contact.methods.items', { returnObjects: true });
  const contactMethods = Array.isArray(contactMethodsData) ? contactMethodsData.map((method, index) => ({
    ...method,
    icon: [Phone, Mail, MessageSquare, Calendar][index]
  })) : [];

  const officesData = t('contact.offices.locations', { returnObjects: true });
  const offices = Array.isArray(officesData) ? officesData : [];

  const industriesData = t('contact.form.industries', { returnObjects: true });
  const industries = Array.isArray(industriesData) ? industriesData : [];

  const inquiryTypesData = t('contact.form.inquiryTypes', { returnObjects: true });
  const inquiryTypes = Array.isArray(inquiryTypesData) ? inquiryTypesData : [];

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-brand-50 via-white to-purple-50 flex items-center justify-center">
        <div className="max-w-2xl mx-auto px-4 text-center">
          <Card className="p-8 border-green-200 bg-gradient-to-br from-green-50 to-white">
            <CardContent className="space-y-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <div className="space-y-4">
                <h1 className="text-3xl font-bold text-gray-900">{t('contact.success.title')}</h1>
                <p className="text-xl text-gray-600">
                  {t('contact.success.message')}
                </p>
                <p className="text-gray-600">
                  {t('contact.success.additional')}
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button onClick={() => setIsSubmitted(false)} className="bg-brand-600 hover:bg-brand-700">
                  {t('contact.success.submitAnother')}
                </Button>
                <Button variant="outline" onClick={() => window.location.href = '/'}>
                  {t('contact.success.returnHome')}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-0">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-brand-50 via-white to-purple-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-6">
            <Badge variant="secondary" className="bg-brand-100 text-brand-800 border-brand-200">
              {t('contact.hero.badge')}
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
              {t('contact.hero.title')}
            </h1>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto">
              {t('contact.hero.subtitle')}
            </p>
          </div>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              {t('contact.methods.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('contact.methods.subtitle')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {contactMethods.map((method, index) => {
              const IconComponent = method.icon;
              return (
                <Card key={index} className="text-center p-6 hover:shadow-lg transition-all group cursor-pointer">
                  <CardHeader className="space-y-4">
                    <div className="w-12 h-12 bg-brand-100 rounded-lg flex items-center justify-center mx-auto group-hover:bg-brand-600 transition-colors">
                      <IconComponent className="w-6 h-6 text-brand-600 group-hover:text-white" />
                    </div>
                    <CardTitle className="text-lg font-semibold text-gray-900">{method.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <p className="text-gray-600 text-sm">{method.description}</p>
                    <p className="font-medium text-brand-600">{method.contact}</p>
                    <p className="text-xs text-gray-500">{method.availability}</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Contact Form */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Form */}
            <Card className="p-8">
              <CardHeader className="space-y-4">
                <CardTitle className="text-2xl font-bold text-gray-900">{t('contact.form.title')}</CardTitle>
                <p className="text-gray-600">
                  {t('contact.form.subtitle')}
                </p>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="firstName">{t('contact.form.fields.firstName')} {t('contact.form.fields.required')}</Label>
                      <Input
                        id="firstName"
                        value={formData.firstName}
                        onChange={(e) => handleInputChange('firstName', e.target.value)}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName">{t('contact.form.fields.lastName')} {t('contact.form.fields.required')}</Label>
                      <Input
                        id="lastName"
                        value={formData.lastName}
                        onChange={(e) => handleInputChange('lastName', e.target.value)}
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">{t('contact.form.fields.email')} {t('contact.form.fields.required')}</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="company">{t('contact.form.fields.company')} {t('contact.form.fields.required')}</Label>
                      <Input
                        id="company"
                        value={formData.company}
                        onChange={(e) => handleInputChange('company', e.target.value)}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="jobTitle">{t('contact.form.fields.jobTitle')}</Label>
                      <Input
                        id="jobTitle"
                        value={formData.jobTitle}
                        onChange={(e) => handleInputChange('jobTitle', e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="industry">{t('contact.form.fields.industry')}</Label>
                      <Select onValueChange={(value) => handleInputChange('industry', value)}>
                        <SelectTrigger>
                          <SelectValue placeholder={t('contact.form.fields.selectIndustry')} />
                        </SelectTrigger>
                        <SelectContent>
                          {industries.map((industry) => (
                            <SelectItem key={industry} value={industry}>{industry}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">{t('contact.form.fields.phone')}</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="inquiryType">{t('contact.form.fields.inquiryType')} {t('contact.form.fields.required')}</Label>
                    <Select onValueChange={(value) => handleInputChange('inquiryType', value)} required>
                      <SelectTrigger>
                        <SelectValue placeholder={t('contact.form.fields.selectInquiryType')} />
                      </SelectTrigger>
                      <SelectContent>
                        {inquiryTypes.map((type) => (
                          <SelectItem key={type} value={type}>{type}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="message">{t('contact.form.fields.message')} {t('contact.form.fields.required')}</Label>
                    <Textarea
                      id="message"
                      rows={4}
                      value={formData.message}
                      onChange={(e) => handleInputChange('message', e.target.value)}
                      placeholder={t('contact.form.fields.messagePlaceholder')}
                      required
                    />
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="newsletter"
                        checked={formData.newsletter}
                        onCheckedChange={(checked) => handleInputChange('newsletter', checked as boolean)}
                      />
                      <Label htmlFor="newsletter" className="text-sm text-gray-600">
                        {t('contact.form.fields.newsletter')}
                      </Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="terms"
                        checked={formData.terms}
                        onCheckedChange={(checked) => handleInputChange('terms', checked as boolean)}
                        required
                      />
                      <Label htmlFor="terms" className="text-sm text-gray-600">
                        {t('contact.form.fields.terms')} {t('contact.form.fields.required')}
                      </Label>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-brand-600 hover:bg-brand-700"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? t('contact.form.fields.sending') : t('contact.form.fields.sendMessage')}
                    {!isSubmitting && <ArrowRight className="w-4 h-4 ml-2" />}
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Contact Info */}
            <div className="space-y-8">
              <Card className="p-8 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                <CardHeader className="space-y-4">
                  <CardTitle className="text-xl font-semibold text-gray-900">{t('contact.whyChoose.title')}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {Array.isArray(t('contact.whyChoose.benefits', { returnObjects: true })) ?
                    t('contact.whyChoose.benefits', { returnObjects: true }).map((benefit, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                        <p className="text-gray-700 text-sm">{benefit}</p>
                      </div>
                    )) : null}
                </CardContent>
              </Card>

              <Card className="p-8">
                <CardHeader className="space-y-4">
                  <CardTitle className="text-xl font-semibold text-gray-900">{t('contact.social.title')}</CardTitle>
                  <p className="text-gray-600">{t('contact.social.subtitle')}</p>
                </CardHeader>
                <CardContent>
                  <div className="flex space-x-4">
                    <a href="#" className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center hover:bg-blue-600 hover:text-white transition-colors">
                      <Linkedin className="w-5 h-5" />
                    </a>
                    <a href="#" className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center hover:bg-blue-600 hover:text-white transition-colors">
                      <Twitter className="w-5 h-5" />
                    </a>
                    <a href="#" className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center hover:bg-blue-600 hover:text-white transition-colors">
                      <Youtube className="w-5 h-5" />
                    </a>
                  </div>
                </CardContent>
              </Card>

              <Card className="p-8">
                <CardHeader className="space-y-4">
                  <CardTitle className="text-xl font-semibold text-gray-900">{t('contact.response.title')}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {Array.isArray(t('contact.response.items', { returnObjects: true })) ?
                    t('contact.response.items', { returnObjects: true }).map((item, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        {[Clock, Users, Globe][index] && React.createElement([Clock, Users, Globe][index], { className: "w-5 h-5 text-blue-600" })}
                        <span className="text-gray-700">{item}</span>
                      </div>
                    )) : null}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Office Locations */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              {t('contact.offices.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('contact.offices.subtitle')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {offices.map((office, index) => (
              <Card key={index} className={`p-6 hover:shadow-lg transition-all ${office.isHeadquarters ? 'border-blue-300 bg-blue-50' : ''}`}>
                <CardHeader className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Building2 className="w-5 h-5 text-blue-600" />
                    </div>
                    <CardTitle className="text-lg font-semibold text-gray-900">{office.city}</CardTitle>
                    {office.isHeadquarters && (
                      <Badge variant="secondary" className="bg-blue-100 text-blue-800 text-xs">HQ</Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start space-x-2">
                    <MapPin className="w-4 h-4 text-gray-400 mt-1 flex-shrink-0" />
                    <p className="text-gray-600 text-sm">{office.address}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Phone className="w-4 h-4 text-gray-400" />
                    <p className="text-gray-600 text-sm">{office.phone}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail className="w-4 h-4 text-gray-400" />
                    <p className="text-gray-600 text-sm">{office.email}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default ContactPage;
