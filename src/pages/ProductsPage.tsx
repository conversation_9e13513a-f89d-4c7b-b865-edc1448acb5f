import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Brain, 
  Workflow, 
  Database, 
  Building2, 
  Search,
  CheckCircle,
  ArrowRight,
  Zap,
  Shield,
  Users,
  BarChart3,
  Settings,
  Link as LinkIcon
} from 'lucide-react';
import { Link } from 'react-router-dom';

const ProductsPage = () => {
  const [activeTab, setActiveTab] = useState('ai-engine');

  const products = [
    {
      id: 'ai-engine',
      icon: Brain,
      title: 'AI-Powered Process Engine',
      subtitle: 'Intelligent Workflow Automation',
      description: 'The core driving force of BPMAX, powered by AI technology to understand and execute complex business processes. Features graphical process modeling that enables business users to easily get started.',
      image: '/images/ai-automation.png',
      features: [
        'Visual Process Designer with AI Assistance',
        'Smart Process Recommendations',
        'Natural Language Process Definition',
        'Auto-Generated Process Documentation',
        'Intelligent Exception Handling',
        'Real-time Process Optimization'
      ],
      benefits: [
        'Reduce process development time by 70%',
        'Enable business users to build processes',
        'Accelerate response to market changes',
        'Lower technical barriers'
      ],
      color: 'blue'
    },
    {
      id: 'form-engine',
      icon: Workflow,
      title: 'Form Engine',
      subtitle: 'Visual Form Designer',
      description: 'Provides a visual form designer with drag-and-drop operations to quickly create various electronic forms that match business processes. Supports rich data types and validation rules.',
      image: '/images/workflow-dashboard.jpg',
      features: [
        'Drag-and-Drop Form Builder',
        'Rich Field Types and Widgets',
        'Advanced Validation Rules',
        'Conditional Logic and Dynamic Fields',
        'Mobile-Responsive Forms',
        'Multi-language Support'
      ],
      benefits: [
        'Eliminate paper forms and scattered Excel files',
        'Ensure structured data collection',
        'Improve data quality and processing efficiency',
        'Enable real-time data validation'
      ],
      color: 'green'
    },
    {
      id: 'data-engine',
      icon: Database,
      title: 'Data Engine',
      subtitle: 'Real-time Data Integration',
      description: 'Responsible for integrating, processing, and analyzing process data. Uses automatic tracking technology to collect various business and system interaction data in real-time during process execution.',
      image: '/images/data-analytics.jpg',
      features: [
        'Real-time Data Collection and Processing',
        'Advanced ETL Capabilities',
        'Data Quality Management',
        'Master Data Management',
        'API-First Architecture',
        'Stream Processing Support'
      ],
      benefits: [
        'Provide data support for process optimization',
        'Help enterprises discover process bottlenecks',
        'Quantify improvement effects',
        'Enable data-driven decision making'
      ],
      color: 'purple'
    },
    {
      id: 'org-modeling',
      icon: Building2,
      title: 'Organization Modeling',
      subtitle: 'Multi-dimensional Organizational Management',
      description: 'Supports enterprises in building multi-dimensional, hierarchically complex organizational models. Accurately reflects enterprise management structure to ensure processes flow correctly between organizational units and roles.',
      image: '/images/team-collaboration.jpg',
      features: [
        'Flexible Organizational Hierarchy',
        'Role-based Access Control',
        'Dynamic Team Management',
        'Multi-tenant Architecture',
        'Delegation and Substitution Rules',
        'Organizational Change Management'
      ],
      benefits: [
        'Achieve precise matching of processes and organizational structure',
        'Ensure compliance and efficiency of process execution',
        'Support complex enterprise structures',
        'Enable flexible role management'
      ],
      color: 'orange'
    },
    {
      id: 'process-mining',
      icon: Search,
      title: 'Process Mining, Tracing & Simulation',
      subtitle: 'Intelligent Process Discovery and Optimization',
      description: 'BPMAX\'s distinctive feature that sets it apart from traditional BPM software. Automatically discovers, monitors, and optimizes actual business processes by analyzing system logs and business data.',
      image: '/images/business-intelligence.jpg',
      features: [
        'Automatic Process Discovery',
        'Real-time Process Monitoring',
        'Process Conformance Checking',
        'Bottleneck Analysis',
        'Process Simulation and Prediction',
        'Continuous Improvement Recommendations'
      ],
      benefits: [
        'Move from "process compliance" to "process intelligence"',
        'Discover hidden process issues',
        'Enable data-driven continuous optimization',
        'Reduce transformation risks'
      ],
      color: 'red'
    }
  ];

  const currentProduct = products.find(p => p.id === activeTab);

  const architectureFeatures = [
    {
      icon: Zap,
      title: 'High Performance',
      description: 'Cloud-native microservices architecture ensuring scalability and reliability'
    },
    {
      icon: Shield,
      title: 'Enterprise Security',
      description: 'Built-in security features with compliance support for various industry standards'
    },
    {
      icon: LinkIcon,
      title: 'Easy Integration',
      description: 'Rich APIs and connectors for seamless integration with existing systems'
    },
    {
      icon: Users,
      title: 'Multi-tenancy',
      description: 'Support for multi-tenant deployments with isolated data and configurations'
    }
  ];

  return (
    <div className="space-y-0">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-6">
            <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
              Five Core Engines
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
              Comprehensive Hyperautomation Platform
            </h1>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto">
              BPMAX combines five powerful engines to deliver end-to-end business process automation and intelligence. 
              Each engine is designed to work seamlessly together, creating a unified platform for digital transformation.
            </p>
          </div>
        </div>
      </section>

      {/* Product Showcase */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
            <TabsList className="grid w-full grid-cols-1 md:grid-cols-3 lg:grid-cols-5 h-auto p-1">
              {products.map((product) => {
                const IconComponent = product.icon;
                return (
                  <TabsTrigger 
                    key={product.id} 
                    value={product.id}
                    className="flex flex-col items-center p-4 space-y-2 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-600"
                  >
                    <IconComponent className="w-6 h-6" />
                    <span className="text-sm font-medium text-center">{product.title.split(' ')[0]} {product.title.split(' ')[1]}</span>
                  </TabsTrigger>
                );
              })}
            </TabsList>

            {products.map((product) => {
              const IconComponent = product.icon;
              return (
                <TabsContent key={product.id} value={product.id} className="space-y-0">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <div className="space-y-8">
                      <div className="space-y-4">
                        <div className="flex items-center space-x-3">
                          <div className={`w-12 h-12 bg-${product.color}-100 rounded-lg flex items-center justify-center`}>
                            <IconComponent className={`w-6 h-6 text-${product.color}-600`} />
                          </div>
                          <div>
                            <h2 className="text-3xl font-bold text-gray-900">{product.title}</h2>
                            <p className="text-lg text-gray-600">{product.subtitle}</p>
                          </div>
                        </div>
                        <p className="text-gray-600 text-lg leading-relaxed">
                          {product.description}
                        </p>
                      </div>

                      <div className="space-y-6">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">Key Features</h3>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                            {product.features.map((feature, index) => (
                              <div key={index} className="flex items-start space-x-2">
                                <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                                <span className="text-sm text-gray-700">{feature}</span>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">Business Benefits</h3>
                          <div className="space-y-2">
                            {product.benefits.map((benefit, index) => (
                              <div key={index} className="flex items-start space-x-2">
                                <ArrowRight className="w-4 h-4 text-blue-500 mt-1 flex-shrink-0" />
                                <span className="text-gray-700">{benefit}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="relative">
                      <img 
                        src={product.image} 
                        alt={product.title} 
                        className="w-full h-auto rounded-2xl shadow-xl"
                      />
                      <div className="absolute -bottom-4 -right-4 bg-white p-4 rounded-xl shadow-lg border">
                        <div className="flex items-center space-x-2">
                          <div className={`w-3 h-3 bg-${product.color}-500 rounded-full animate-pulse`}></div>
                          <span className="text-sm font-medium text-gray-700">Engine Active</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              );
            })}
          </Tabs>
        </div>
      </section>

      {/* Architecture Features */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Advanced Technical Architecture
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Built on cloud-native microservices architecture with AI capabilities at its core
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {architectureFeatures.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <Card key={index} className="text-center p-6 hover:shadow-lg transition-all">
                  <CardHeader className="space-y-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto">
                      <IconComponent className="w-6 h-6 text-blue-600" />
                    </div>
                    <CardTitle className="text-lg font-semibold">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 text-sm">{feature.description}</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Integration Ecosystem */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                  Seamless Integration Ecosystem
                </h2>
                <p className="text-xl text-gray-600">
                  Connect BPMAX with your existing systems and third-party applications through our comprehensive integration capabilities.
                </p>
              </div>

              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-900">Enterprise Systems</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• ERP (SAP, Oracle)</li>
                      <li>• CRM (Salesforce, Dynamics)</li>
                      <li>• HRM (Workday, ADP)</li>
                    </ul>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-900">Communication</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Email Systems</li>
                      <li>• Messaging Platforms</li>
                      <li>• Video Conferencing</li>
                    </ul>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-900">Databases</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• SQL & NoSQL Databases</li>
                      <li>• Data Warehouses</li>
                      <li>• Cloud Storage</li>
                    </ul>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-900">Analytics</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• BI Tools</li>
                      <li>• Reporting Systems</li>
                      <li>• Dashboard Platforms</li>
                    </ul>
                  </div>
                </div>
              </div>

              <Button className="bg-blue-600 hover:bg-blue-700" asChild>
                <Link to="/contact">
                  Discuss Integration Requirements
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Link>
              </Button>
            </div>

            <div className="relative">
              <img 
                src="/images/software-platform.png" 
                alt="Integration Ecosystem" 
                className="w-full h-auto rounded-2xl shadow-xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="space-y-8">
            <h2 className="text-3xl md:text-4xl font-bold">
              Ready to Experience BPMAX?
            </h2>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              See how our five core engines can transform your business processes. 
              Schedule a personalized demo today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3" asChild>
                <Link to="/contact">
                  Schedule Product Demo
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3" asChild>
                <Link to="/solutions">View Solutions</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ProductsPage;
