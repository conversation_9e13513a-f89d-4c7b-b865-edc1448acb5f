import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Home, 
  Package, 
  Phone, 
  Search,
  ArrowRight,
  CheckCircle
} from 'lucide-react';

const NotFoundPage = () => {
  const { t } = useTranslation();

  const suggestions = t('error.notFound.suggestions.items', { returnObjects: true });

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4">
      <div className="max-w-4xl w-full">
        <div className="text-center space-y-8">
          {/* 404 Number */}
          <div className="relative">
            <h1 className="text-9xl md:text-[12rem] font-bold text-gray-200 select-none">
              404
            </h1>
            <div className="absolute inset-0 flex items-center justify-center">
              <Search className="w-16 h-16 md:w-20 md:h-20 text-blue-600" />
            </div>
          </div>

          {/* Error Message */}
          <div className="space-y-4">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              {t('error.notFound.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              {t('error.notFound.subtitle')}
            </p>
            <p className="text-gray-500 max-w-xl mx-auto">
              {t('error.notFound.description')}
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700" asChild>
              <Link to="/">
                <Home className="w-5 h-5 mr-2" />
                {t('error.notFound.backHome')}
              </Link>
            </Button>
            <Button size="lg" variant="outline" className="border-blue-300 text-blue-600 hover:bg-blue-50" asChild>
              <Link to="/products">
                <Package className="w-5 h-5 mr-2" />
                {t('error.notFound.exploreProducts')}
              </Link>
            </Button>
            <Button size="lg" variant="outline" className="border-blue-300 text-blue-600 hover:bg-blue-50" asChild>
              <Link to="/contact">
                <Phone className="w-5 h-5 mr-2" />
                {t('error.notFound.contactUs')}
              </Link>
            </Button>
          </div>

          {/* Suggestions */}
          <Card className="max-w-2xl mx-auto mt-12 border-blue-200 bg-gradient-to-br from-blue-50 to-white">
            <CardHeader>
              <CardTitle className="text-xl font-semibold text-gray-900 text-center">
                {t('error.notFound.suggestions.title')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {suggestions.map((suggestion: string, index: number) => (
                  <div key={index} className="flex items-start space-x-3">
                    <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{suggestion}</span>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 pt-6 border-t border-blue-200">
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <Link 
                    to="/products" 
                    className="flex items-center justify-center p-3 rounded-lg border border-blue-200 hover:bg-blue-50 transition-colors group"
                  >
                    <Package className="w-5 h-5 text-blue-600 mr-2" />
                    <span className="text-blue-600 font-medium">产品</span>
                    <ArrowRight className="w-4 h-4 text-blue-600 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Link>
                  
                  <Link 
                    to="/solutions" 
                    className="flex items-center justify-center p-3 rounded-lg border border-blue-200 hover:bg-blue-50 transition-colors group"
                  >
                    <Search className="w-5 h-5 text-blue-600 mr-2" />
                    <span className="text-blue-600 font-medium">解决方案</span>
                    <ArrowRight className="w-4 h-4 text-blue-600 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Link>
                  
                  <Link 
                    to="/cases" 
                    className="flex items-center justify-center p-3 rounded-lg border border-blue-200 hover:bg-blue-50 transition-colors group"
                  >
                    <CheckCircle className="w-5 h-5 text-blue-600 mr-2" />
                    <span className="text-blue-600 font-medium">案例</span>
                    <ArrowRight className="w-4 h-4 text-blue-600 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default NotFoundPage;
