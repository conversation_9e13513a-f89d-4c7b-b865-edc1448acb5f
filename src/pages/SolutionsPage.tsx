import { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ShoppingCart, 
  Factory, 
  Building, 
  Home, 
  Heart,
  TrendingUp,
  CheckCircle,
  ArrowRight,
  Clock,
  DollarSign,
  Users,
  BarChart3
} from 'lucide-react';

const SolutionsPage = () => {
  const [activeIndustry, setActiveIndustry] = useState('retail');

  const industries = [
    {
      id: 'retail',
      icon: ShoppingCart,
      name: 'Retail & F&B',
      title: 'Chain Retail & Food Service Solutions',
      description: 'Streamline operations across multiple locations with intelligent process automation tailored for retail and food service businesses.',
      image: '/images/team-collaboration.jpg',
      challenges: [
        'Managing operations across hundreds of locations',
        'Ensuring consistent service quality and compliance',
        'Coordinating supply chain and inventory management',
        'Standardizing training and operational procedures'
      ],
      solutions: [
        'Multi-location operational management',
        'Automated compliance monitoring',
        'Supply chain optimization',
        'Staff training and certification workflows',
        'Customer service standardization',
        'Real-time performance monitoring'
      ],
      benefits: [
        '40% reduction in operational costs',
        '60% faster new store onboarding',
        '95% compliance rate achievement',
        '30% improvement in customer satisfaction'
      ],
      caseStudy: {
        company: 'Chabaidao (茶百道)',
        challenge: 'Managing 8000+ stores with consistent quality and operations',
        solution: 'Implemented BPMAX for end-to-end store operations automation',
        result: 'Reduced operational overhead by 35% while maintaining quality standards'
      }
    },
    {
      id: 'manufacturing',
      icon: Factory,
      name: 'Manufacturing',
      title: 'Smart Manufacturing Solutions',
      description: 'Transform manufacturing operations with AI-driven process automation, quality management, and supply chain optimization.',
      image: '/images/process-flowchart.jpg',
      challenges: [
        'Complex production planning and scheduling',
        'Quality control and compliance management',
        'Supply chain disruptions and delays',
        'Equipment maintenance and downtime'
      ],
      solutions: [
        'Production planning optimization',
        'Quality management systems',
        'Supplier relationship management',
        'Predictive maintenance workflows',
        'Inventory management automation',
        'Regulatory compliance tracking'
      ],
      benefits: [
        '25% increase in production efficiency',
        '50% reduction in quality defects',
        '30% decrease in inventory costs',
        '20% reduction in equipment downtime'
      ],
      caseStudy: {
        company: 'Leading Electronics Manufacturer',
        challenge: 'Complex multi-stage production with strict quality requirements',
        solution: 'Deployed BPMAX for integrated production and quality management',
        result: 'Achieved 99.5% quality compliance with 25% efficiency improvement'
      }
    },
    {
      id: 'financial',
      icon: Building,
      name: 'Financial Services',
      title: 'Financial Services Automation',
      description: 'Enhance financial operations with automated compliance, risk management, and customer service processes.',
      image: '/images/business-intelligence.jpg',
      challenges: [
        'Strict regulatory compliance requirements',
        'Complex approval workflows',
        'Risk assessment and management',
        'Customer onboarding and KYC processes'
      ],
      solutions: [
        'Regulatory compliance automation',
        'Risk assessment workflows',
        'Customer onboarding optimization',
        'Loan processing automation',
        'Audit trail management',
        'Real-time monitoring and reporting'
      ],
      benefits: [
        '70% faster loan processing',
        '99% regulatory compliance',
        '45% reduction in operational risk',
        '60% improvement in customer onboarding time'
      ],
      caseStudy: {
        company: 'Regional Commercial Bank',
        challenge: 'Manual loan approval processes taking weeks to complete',
        solution: 'Automated end-to-end loan processing with BPMAX',
        result: 'Reduced processing time from 3 weeks to 3 days'
      }
    },
    {
      id: 'real-estate',
      icon: Home,
      name: 'Real Estate',
      title: 'Real Estate Management Solutions',
      description: 'Optimize property management, sales processes, and customer relationships with comprehensive automation.',
      image: '/images/executive-meeting.jpg',
      challenges: [
        'Complex property development workflows',
        'Sales and leasing process management',
        'Property maintenance coordination',
        'Customer relationship management'
      ],
      solutions: [
        'Property development project management',
        'Sales process automation',
        'Tenant management systems',
        'Maintenance request workflows',
        'Contract management automation',
        'Customer service optimization'
      ],
      benefits: [
        '50% faster property sales cycles',
        '40% improvement in tenant satisfaction',
        '30% reduction in maintenance costs',
        '35% increase in operational efficiency'
      ],
      caseStudy: {
        company: 'National Property Developer',
        challenge: 'Managing complex development projects across multiple cities',
        solution: 'Implemented BPMAX for project and sales management',
        result: 'Delivered projects 20% faster with improved quality control'
      }
    },
    {
      id: 'pharma',
      icon: Heart,
      name: 'Life Sciences',
      title: 'Pharmaceutical & Healthcare Solutions',
      description: 'Ensure compliance and optimize operations in pharmaceutical and healthcare organizations with specialized workflows.',
      image: '/images/data-analytics.jpg',
      challenges: [
        'Stringent regulatory compliance (FDA, GMP)',
        'Clinical trial management',
        'Drug development lifecycle tracking',
        'Quality assurance and validation'
      ],
      solutions: [
        'Regulatory submission workflows',
        'Clinical trial management',
        'Quality management systems',
        'Document control and validation',
        'Adverse event reporting',
        'Compliance monitoring and reporting'
      ],
      benefits: [
        '60% faster regulatory submissions',
        '99.9% compliance rate',
        '40% reduction in time-to-market',
        '50% improvement in audit readiness'
      ],
      caseStudy: {
        company: 'Pharmaceutical Company',
        challenge: 'Complex regulatory approval processes for new drugs',
        solution: 'Streamlined regulatory workflows with BPMAX',
        result: 'Reduced approval time by 6 months while ensuring full compliance'
      }
    }
  ];

  const currentIndustry = industries.find(i => i.id === activeIndustry);

  const implementationSteps = [
    {
      step: '1',
      title: 'Discovery & Assessment',
      description: 'Analyze current processes and identify automation opportunities',
      duration: '2-4 weeks'
    },
    {
      step: '2',
      title: 'Solution Design',
      description: 'Create customized workflows and integration plans',
      duration: '3-6 weeks'
    },
    {
      step: '3',
      title: 'Pilot Implementation',
      description: 'Deploy in a controlled environment with selected processes',
      duration: '4-8 weeks'
    },
    {
      step: '4',
      title: 'Full Deployment',
      description: 'Roll out across the entire organization with training',
      duration: '6-12 weeks'
    },
    {
      step: '5',
      title: 'Optimization',
      description: 'Continuous monitoring and process improvement',
      duration: 'Ongoing'
    }
  ];

  return (
    <div className="space-y-0">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-6">
            <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
              Industry Solutions
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
              Tailored Solutions for Every Industry
            </h1>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto">
              BPMAX delivers industry-specific hyperautomation solutions that address unique challenges 
              and regulatory requirements across various sectors.
            </p>
          </div>
        </div>
      </section>

      {/* Industry Solutions */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Tabs value={activeIndustry} onValueChange={setActiveIndustry} className="space-y-8">
            <TabsList className="grid w-full grid-cols-2 md:grid-cols-3 lg:grid-cols-5 h-auto p-1">
              {industries.map((industry) => {
                const IconComponent = industry.icon;
                return (
                  <TabsTrigger 
                    key={industry.id} 
                    value={industry.id}
                    className="flex flex-col items-center p-4 space-y-2 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-600"
                  >
                    <IconComponent className="w-6 h-6" />
                    <span className="text-sm font-medium text-center">{industry.name}</span>
                  </TabsTrigger>
                );
              })}
            </TabsList>

            {industries.map((industry) => {
              const IconComponent = industry.icon;
              return (
                <TabsContent key={industry.id} value={industry.id} className="space-y-12">
                  {/* Industry Overview */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <div className="space-y-8">
                      <div className="space-y-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <IconComponent className="w-6 h-6 text-blue-600" />
                          </div>
                          <div>
                            <h2 className="text-3xl font-bold text-gray-900">{industry.title}</h2>
                            <p className="text-lg text-gray-600">{industry.name}</p>
                          </div>
                        </div>
                        <p className="text-gray-600 text-lg leading-relaxed">
                          {industry.description}
                        </p>
                      </div>

                      <div className="grid grid-cols-2 gap-6">
                        <div className="space-y-3">
                          <div className="flex items-center space-x-2">
                            <TrendingUp className="w-5 h-5 text-green-500" />
                            <span className="font-semibold text-gray-900">Efficiency Gain</span>
                          </div>
                          <p className="text-2xl font-bold text-green-600">25-40%</p>
                        </div>
                        <div className="space-y-3">
                          <div className="flex items-center space-x-2">
                            <DollarSign className="w-5 h-5 text-blue-500" />
                            <span className="font-semibold text-gray-900">Cost Reduction</span>
                          </div>
                          <p className="text-2xl font-bold text-blue-600">30-50%</p>
                        </div>
                      </div>
                    </div>

                    <div className="relative">
                      <img 
                        src={industry.image} 
                        alt={industry.title} 
                        className="w-full h-auto rounded-2xl shadow-xl"
                      />
                    </div>
                  </div>

                  {/* Challenges & Solutions */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                    <Card className="p-6">
                      <CardHeader>
                        <CardTitle className="text-xl font-semibold text-red-600">Industry Challenges</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        {industry.challenges.map((challenge, index) => (
                          <div key={index} className="flex items-start space-x-2">
                            <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                            <span className="text-gray-700">{challenge}</span>
                          </div>
                        ))}
                      </CardContent>
                    </Card>

                    <Card className="p-6">
                      <CardHeader>
                        <CardTitle className="text-xl font-semibold text-green-600">BPMAX Solutions</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        {industry.solutions.map((solution, index) => (
                          <div key={index} className="flex items-start space-x-2">
                            <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-700">{solution}</span>
                          </div>
                        ))}
                      </CardContent>
                    </Card>
                  </div>

                  {/* Benefits */}
                  <div className="bg-gray-50 p-8 rounded-2xl">
                    <h3 className="text-2xl font-bold text-gray-900 mb-6">Measurable Business Impact</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                      {industry.benefits.map((benefit, index) => (
                        <div key={index} className="text-center space-y-2">
                          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto">
                            <BarChart3 className="w-6 h-6 text-blue-600" />
                          </div>
                          <p className="font-semibold text-gray-900">{benefit}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Case Study */}
                  <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
                    <CardHeader>
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800">Customer Success</Badge>
                        <CardTitle className="text-xl font-semibold text-gray-900">{industry.caseStudy.company}</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Challenge</h4>
                          <p className="text-gray-700 text-sm">{industry.caseStudy.challenge}</p>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Solution</h4>
                          <p className="text-gray-700 text-sm">{industry.caseStudy.solution}</p>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Result</h4>
                          <p className="text-gray-700 text-sm">{industry.caseStudy.result}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              );
            })}
          </Tabs>
        </div>
      </section>

      {/* Implementation Process */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Proven Implementation Methodology
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our structured approach ensures successful deployment and maximum ROI
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
            {implementationSteps.map((step, index) => (
              <div key={index} className="relative">
                <Card className="text-center p-6 h-full">
                  <CardContent className="space-y-4">
                    <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto text-lg font-bold">
                      {step.step}
                    </div>
                    <h3 className="font-semibold text-gray-900">{step.title}</h3>
                    <p className="text-sm text-gray-600">{step.description}</p>
                    <div className="flex items-center justify-center space-x-1 text-xs text-blue-600">
                      <Clock className="w-3 h-3" />
                      <span>{step.duration}</span>
                    </div>
                  </CardContent>
                </Card>
                {index < implementationSteps.length - 1 && (
                  <div className="hidden md:block absolute top-1/2 -right-4 w-8 h-0.5 bg-blue-200 transform -translate-y-1/2 z-10">
                    <ArrowRight className="w-4 h-4 text-blue-400 absolute -top-2 right-0" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="space-y-8">
            <h2 className="text-3xl md:text-4xl font-bold">
              Ready to Transform Your Industry Operations?
            </h2>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Discover how BPMAX can address your industry-specific challenges. 
              Schedule a consultation with our experts today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3" asChild>
                <Link to="/contact">
                  Schedule Industry Consultation
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3" asChild>
                <Link to="/cases">View Customer Cases</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default SolutionsPage;
