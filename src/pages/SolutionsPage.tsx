import { useState } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ShoppingCart,
  Factory,
  Building,
  Home,
  Heart,
  TrendingUp,
  CheckCircle,
  ArrowRight,
  Clock,
  DollarSign,
  Users,
  BarChart3
} from 'lucide-react';

const SolutionsPage = () => {
  const [activeIndustry, setActiveIndustry] = useState('retail');
  const { t } = useTranslation();

  const industries = [
    {
      id: 'retail',
      icon: ShoppingCart,
      name: t('solutions.industries.retail.name'),
      title: t('solutions.industries.retail.title'),
      description: t('solutions.industries.retail.description'),
      image: '/images/team-collaboration.jpg',
      challenges: t('solutions.industries.retail.challenges', { returnObjects: true }),
      solutions: t('solutions.industries.retail.solutions', { returnObjects: true }),
      benefits: t('solutions.industries.retail.benefits', { returnObjects: true }),
      caseStudy: {
        company: t('solutions.industries.retail.caseStudy.company'),
        challenge: t('solutions.industries.retail.caseStudy.challenge'),
        solution: t('solutions.industries.retail.caseStudy.solution'),
        result: t('solutions.industries.retail.caseStudy.result')
      }
    },
    {
      id: 'manufacturing',
      icon: Factory,
      name: t('solutions.industries.manufacturing.name'),
      title: t('solutions.industries.manufacturing.title'),
      description: t('solutions.industries.manufacturing.description'),
      image: '/images/process-flowchart.jpg',
      challenges: t('solutions.industries.manufacturing.challenges', { returnObjects: true }),
      solutions: t('solutions.industries.manufacturing.solutions', { returnObjects: true }),
      benefits: t('solutions.industries.manufacturing.benefits', { returnObjects: true }),
      caseStudy: {
        company: t('solutions.industries.manufacturing.caseStudy.company'),
        challenge: t('solutions.industries.manufacturing.caseStudy.challenge'),
        solution: t('solutions.industries.manufacturing.caseStudy.solution'),
        result: t('solutions.industries.manufacturing.caseStudy.result')
      }
    },
    {
      id: 'financial',
      icon: Building,
      name: t('solutions.industries.financial.name'),
      title: t('solutions.industries.financial.title'),
      description: t('solutions.industries.financial.description'),
      image: '/images/business-intelligence.jpg',
      challenges: t('solutions.industries.financial.challenges', { returnObjects: true }),
      solutions: t('solutions.industries.financial.solutions', { returnObjects: true }),
      benefits: t('solutions.industries.financial.benefits', { returnObjects: true }),
      caseStudy: {
        company: t('solutions.industries.financial.caseStudy.company'),
        challenge: t('solutions.industries.financial.caseStudy.challenge'),
        solution: t('solutions.industries.financial.caseStudy.solution'),
        result: t('solutions.industries.financial.caseStudy.result')
      }
    },
    {
      id: 'real-estate',
      icon: Home,
      name: t('solutions.industries.realEstate.name'),
      title: t('solutions.industries.realEstate.title'),
      description: t('solutions.industries.realEstate.description'),
      image: '/images/executive-meeting.jpg',
      challenges: t('solutions.industries.realEstate.challenges', { returnObjects: true }),
      solutions: t('solutions.industries.realEstate.solutions', { returnObjects: true }),
      benefits: t('solutions.industries.realEstate.benefits', { returnObjects: true }),
      caseStudy: {
        company: t('solutions.industries.realEstate.caseStudy.company'),
        challenge: t('solutions.industries.realEstate.caseStudy.challenge'),
        solution: t('solutions.industries.realEstate.caseStudy.solution'),
        result: t('solutions.industries.realEstate.caseStudy.result')
      }
    },
    {
      id: 'pharma',
      icon: Heart,
      name: t('solutions.industries.lifeSciences.name'),
      title: t('solutions.industries.lifeSciences.title'),
      description: t('solutions.industries.lifeSciences.description'),
      image: '/images/data-analytics.jpg',
      challenges: t('solutions.industries.lifeSciences.challenges', { returnObjects: true }),
      solutions: t('solutions.industries.lifeSciences.solutions', { returnObjects: true }),
      benefits: t('solutions.industries.lifeSciences.benefits', { returnObjects: true }),
      caseStudy: {
        company: t('solutions.industries.lifeSciences.caseStudy.company'),
        challenge: t('solutions.industries.lifeSciences.caseStudy.challenge'),
        solution: t('solutions.industries.lifeSciences.caseStudy.solution'),
        result: t('solutions.industries.lifeSciences.caseStudy.result')
      }
    }
  ];

  const currentIndustry = industries.find(i => i.id === activeIndustry);

  const implementationSteps = t('solutions.implementation.steps', { returnObjects: true }).map((step, index) => ({
    step: (index + 1).toString(),
    title: step.title,
    description: step.description,
    duration: step.duration
  }));

  return (
    <div className="space-y-0">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-6">
            <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
              {t('solutions.hero.badge')}
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
              {t('solutions.hero.title')}
            </h1>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto">
              {t('solutions.hero.subtitle')}
            </p>
          </div>
        </div>
      </section>

      {/* Industry Solutions */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Tabs value={activeIndustry} onValueChange={setActiveIndustry} className="space-y-8">
            <TabsList className="grid w-full grid-cols-2 md:grid-cols-3 lg:grid-cols-5 h-auto p-1">
              {industries.map((industry) => {
                const IconComponent = industry.icon;
                return (
                  <TabsTrigger
                    key={industry.id}
                    value={industry.id}
                    className="flex flex-col items-center p-4 space-y-2 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-600"
                  >
                    <IconComponent className="w-6 h-6" />
                    <span className="text-sm font-medium text-center">{industry.name}</span>
                  </TabsTrigger>
                );
              })}
            </TabsList>

            {industries.map((industry) => {
              const IconComponent = industry.icon;
              return (
                <TabsContent key={industry.id} value={industry.id} className="space-y-12">
                  {/* Industry Overview */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <div className="space-y-8">
                      <div className="space-y-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <IconComponent className="w-6 h-6 text-blue-600" />
                          </div>
                          <div>
                            <h2 className="text-3xl font-bold text-gray-900">{industry.title}</h2>
                            <p className="text-lg text-gray-600">{industry.name}</p>
                          </div>
                        </div>
                        <p className="text-gray-600 text-lg leading-relaxed">
                          {industry.description}
                        </p>
                      </div>

                      <div className="grid grid-cols-2 gap-6">
                        <div className="space-y-3">
                          <div className="flex items-center space-x-2">
                            <TrendingUp className="w-5 h-5 text-green-500" />
                            <span className="font-semibold text-gray-900">{t('solutions.common.efficiencyGain')}</span>
                          </div>
                          <p className="text-2xl font-bold text-green-600">25-40%</p>
                        </div>
                        <div className="space-y-3">
                          <div className="flex items-center space-x-2">
                            <DollarSign className="w-5 h-5 text-blue-500" />
                            <span className="font-semibold text-gray-900">{t('solutions.common.costReduction')}</span>
                          </div>
                          <p className="text-2xl font-bold text-blue-600">30-50%</p>
                        </div>
                      </div>
                    </div>

                    <div className="relative">
                      <img
                        src={industry.image}
                        alt={industry.title}
                        className="w-full h-auto rounded-2xl shadow-xl"
                      />
                    </div>
                  </div>

                  {/* Challenges & Solutions */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                    <Card className="p-6">
                      <CardHeader>
                        <CardTitle className="text-xl font-semibold text-red-600">{t('solutions.common.industryChallenges')}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        {industry.challenges.map((challenge, index) => (
                          <div key={index} className="flex items-start space-x-2">
                            <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                            <span className="text-gray-700">{challenge}</span>
                          </div>
                        ))}
                      </CardContent>
                    </Card>

                    <Card className="p-6">
                      <CardHeader>
                        <CardTitle className="text-xl font-semibold text-green-600">{t('solutions.common.bpmaxSolutions')}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        {industry.solutions.map((solution, index) => (
                          <div key={index} className="flex items-start space-x-2">
                            <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-700">{solution}</span>
                          </div>
                        ))}
                      </CardContent>
                    </Card>
                  </div>

                  {/* Benefits */}
                  <div className="bg-gray-50 p-8 rounded-2xl">
                    <h3 className="text-2xl font-bold text-gray-900 mb-6">{t('solutions.common.measurableImpact')}</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                      {industry.benefits.map((benefit, index) => (
                        <div key={index} className="text-center space-y-2">
                          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto">
                            <BarChart3 className="w-6 h-6 text-blue-600" />
                          </div>
                          <p className="font-semibold text-gray-900">{benefit}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Case Study */}
                  <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
                    <CardHeader>
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800">{t('solutions.common.customerSuccess')}</Badge>
                        <CardTitle className="text-xl font-semibold text-gray-900">{industry.caseStudy.company}</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">{t('solutions.common.challenge')}</h4>
                          <p className="text-gray-700 text-sm">{industry.caseStudy.challenge}</p>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">{t('solutions.common.solution')}</h4>
                          <p className="text-gray-700 text-sm">{industry.caseStudy.solution}</p>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">{t('solutions.common.result')}</h4>
                          <p className="text-gray-700 text-sm">{industry.caseStudy.result}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              );
            })}
          </Tabs>
        </div>
      </section>

      {/* Implementation Process */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              {t('solutions.implementation.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('solutions.implementation.subtitle')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
            {implementationSteps.map((step, index) => (
              <div key={index} className="relative">
                <Card className="text-center p-6 h-full">
                  <CardContent className="space-y-4">
                    <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto text-lg font-bold">
                      {step.step}
                    </div>
                    <h3 className="font-semibold text-gray-900">{step.title}</h3>
                    <p className="text-sm text-gray-600">{step.description}</p>
                    <div className="flex items-center justify-center space-x-1 text-xs text-blue-600">
                      <Clock className="w-3 h-3" />
                      <span>{step.duration}</span>
                    </div>
                  </CardContent>
                </Card>
                {index < implementationSteps.length - 1 && (
                  <div className="hidden md:block absolute top-1/2 -right-4 w-8 h-0.5 bg-blue-200 transform -translate-y-1/2 z-10">
                    <ArrowRight className="w-4 h-4 text-blue-400 absolute -top-2 right-0" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="space-y-8">
            <h2 className="text-3xl md:text-4xl font-bold">
              {t('solutions.cta.title')}
            </h2>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              {t('solutions.cta.subtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3" asChild>
                <Link to="/contact">
                  {t('solutions.cta.scheduleConsultation')}
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3" asChild>
                <Link to="/cases">{t('solutions.cta.viewCases')}</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default SolutionsPage;
