import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Brain,
  Zap,
  Target,
  TrendingUp,
  Users,
  Shield,
  ArrowRight,
  CheckCircle,
  BarChart3,
  Workflow,
  Database,
  Building2,
  Search
} from 'lucide-react';

const HomePage = () => {
  const { t } = useTranslation();

  const features = [
    {
      icon: Brain,
      title: t('features.aiProcessEngine.title'),
      description: t('features.aiProcessEngine.description'),
      color: 'text-brand-600'
    },
    {
      icon: Workflow,
      title: t('features.formEngine.title'),
      description: t('features.formEngine.description'),
      color: 'text-green-600'
    },
    {
      icon: Database,
      title: t('features.dataEngine.title'),
      description: t('features.dataEngine.description'),
      color: 'text-purple-600'
    },
    {
      icon: Building2,
      title: t('features.orgModeling.title'),
      description: t('features.orgModeling.description'),
      color: 'text-orange-600'
    },
    {
      icon: Search,
      title: t('features.processMining.title'),
      description: t('features.processMining.description'),
      color: 'text-red-600'
    }
  ];

  const benefits = [
    {
      icon: TrendingUp,
      title: t('benefits.efficiency.title'),
      description: t('benefits.efficiency.description')
    },
    {
      icon: Target,
      title: t('benefits.implementation.title'),
      description: t('benefits.implementation.description')
    },
    {
      icon: Shield,
      title: t('benefits.security.title'),
      description: t('benefits.security.description')
    },
    {
      icon: Users,
      title: t('benefits.support.title'),
      description: t('benefits.support.description')
    }
  ];

  const industries = [
    t('industries.retail'),
    t('industries.manufacturing'),
    t('industries.financial'),
    t('industries.realEstate'),
    t('industries.lifeSciences'),
    t('industries.government')
  ];

  return (
    <div className="space-y-0">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-brand-50 via-white to-purple-50 min-h-screen flex items-center">
        <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] -z-10" />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <Badge variant="secondary" className="bg-brand-100 text-brand-800 border-brand-200">
                  {t('hero.badge')}
                </Badge>
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                  {t('hero.title')}{' '}
                  <span className="bg-gradient-to-r from-brand-600 to-purple-600 bg-clip-text text-transparent">
                    {t('hero.titleHighlight')}
                  </span>
                  {' '}{t('hero.titleEnd')}
                </h1>
                <p className="text-xl text-gray-600 leading-relaxed">
                  {t('hero.description')}
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" className="bg-brand-600 hover:bg-brand-700 text-white px-8 py-3 transition-all duration-200 group" asChild>
                  <Link to="/contact">
                    {t('hero.requestFreeDemo')}
                    <ArrowRight className="w-5 h-5 ml-2 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>
                <Button variant="outline" size="lg" className="px-8 py-3 transition-all duration-200" asChild>
                  <Link to="/products">{t('hero.exploreProducts')}</Link>
                </Button>
              </div>

              <div className="flex items-center space-x-8 text-sm text-gray-600">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>{t('hero.freeTrial30')}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>{t('hero.noCreditCard')}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>{t('hero.support247')}</span>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="w-full h-auto transition-transform duration-300 hover:scale-105">
                <img
                  src="/images/bpmaxdto.svg"
                  alt="BPMAX Hyperautomation Platform"
                  className="w-full h-auto animate-float svg-shadow"
                />
              </div>
              <div className="absolute -bottom-6 -left-6 bg-white p-4 rounded-xl shadow-lg border transition-transform duration-300 hover:scale-105">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-purple-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-gray-700">{t('hero.aiEngineActive')}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Core Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              {t('features.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('features.subtitle')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <Card key={index} className="group hover:shadow-lg transition-all duration-300 border-gray-200 hover:border-brand-200">
                  <CardHeader className="space-y-4">
                    <div className="w-12 h-12 rounded-lg bg-gray-50 flex items-center justify-center group-hover:bg-brand-50 transition-colors">
                      <IconComponent className={`w-6 h-6 ${feature.color} transition-transform group-hover:scale-110`} />
                    </div>
                    <CardTitle className="text-xl font-semibold text-gray-900">
                      {feature.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          <div className="text-center mt-12">
            <Button variant="outline" size="lg" asChild>
              <Link to="/products">
                {t('features.learnMore')}
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Business Value Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                  {t('benefits.title')}
                </h2>
                <p className="text-xl text-gray-600">
                  {t('benefits.subtitle')}
                </p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                {benefits.map((benefit, index) => {
                  const IconComponent = benefit.icon;
                  return (
                    <div key={index} className="space-y-3">
                      <div className="w-10 h-10 bg-brand-100 rounded-lg flex items-center justify-center">
                        <IconComponent className="w-5 h-5 text-brand-600" />
                      </div>
                      <h3 className="font-semibold text-gray-900">{benefit.title}</h3>
                      <p className="text-sm text-gray-600">{benefit.description}</p>
                    </div>
                  );
                })}
              </div>

              <Button className="bg-brand-600 hover:bg-brand-700" asChild>
                <Link to="/cases">
                  {t('benefits.viewCases')}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Link>
              </Button>
            </div>

            <div className="relative">
              <img
                src="/images/data-analytics.jpg"
                alt="Business Analytics Dashboard"
                className="w-full h-auto rounded-2xl shadow-xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Industries Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              {t('industries.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('industries.subtitle')}
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {industries.map((industry, index) => (
              <Card key={index} className="text-center p-6 hover:shadow-md transition-all cursor-pointer">
                <CardContent className="p-0">
                  <p className="font-medium text-gray-700">{industry}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-12">
            <Button variant="outline" size="lg" asChild>
              <Link to="/solutions">
                {t('industries.exploreSolutions')}
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-brand-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="space-y-8">
            <h2 className="text-3xl md:text-4xl font-bold">
              {t('cta.title')}
            </h2>
            <p className="text-xl text-brand-100 max-w-3xl mx-auto">
              {t('cta.subtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" className="bg-white text-brand-600 hover:bg-gray-100 px-8 py-3" asChild>
                <Link to="/contact">
                  {t('cta.scheduleDemo')}
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
              <Button size="lg" className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-brand-600 px-8 py-3" asChild>
                <Link to="/contact">{t('cta.startFreeTrial')}</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
