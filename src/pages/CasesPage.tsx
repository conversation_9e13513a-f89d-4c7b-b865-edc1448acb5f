import { useState } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ShoppingCart,
  Factory,
  Building,
  Home,
  Heart,
  TrendingUp,
  Users,
  Clock,
  DollarSign,
  BarChart3,
  ArrowRight,
  Quote,
  CheckCircle,
  Award,
  Shield
} from 'lucide-react';

const CasesPage = () => {
  const [activeFilter, setActiveFilter] = useState('all');
  const { t } = useTranslation();

  const caseStudies = [
    {
      id: 'chabaidao',
      company: t('cases.caseStudies.chabaidao.company'),
      industry: 'retail',
      logo: '/images/team-collaboration.jpg',
      title: t('cases.caseStudies.chabaidao.title'),
      subtitle: t('cases.caseStudies.chabaidao.subtitle'),
      challenge: t('cases.caseStudies.chabaidao.challenge'),
      solution: t('cases.caseStudies.chabaidao.solution'),
      results: t('cases.caseStudies.chabaidao.results', { returnObjects: true }),
      metrics: [
        { label: t('cases.caseStudies.chabaidao.metrics.0.label'), value: t('cases.caseStudies.chabaidao.metrics.0.value'), icon: ShoppingCart },
        { label: t('cases.caseStudies.chabaidao.metrics.1.label'), value: t('cases.caseStudies.chabaidao.metrics.1.value'), icon: DollarSign },
        { label: t('cases.caseStudies.chabaidao.metrics.2.label'), value: t('cases.caseStudies.chabaidao.metrics.2.value'), icon: Clock },
        { label: t('cases.caseStudies.chabaidao.metrics.3.label'), value: t('cases.caseStudies.chabaidao.metrics.3.value'), icon: CheckCircle }
      ],
      testimonial: {
        quote: t('cases.caseStudies.chabaidao.testimonial.quote'),
        author: t('cases.caseStudies.chabaidao.testimonial.author'),
        position: t('cases.caseStudies.chabaidao.testimonial.position')
      },
      implementation: {
        duration: t('cases.caseStudies.chabaidao.implementation.duration'),
        scope: t('cases.caseStudies.chabaidao.implementation.scope'),
        team: t('cases.caseStudies.chabaidao.implementation.team')
      }
    },
    {
      id: 'electronics-manufacturer',
      company: t('cases.caseStudies.electronicsManufacturer.company'),
      industry: 'manufacturing',
      logo: '/images/process-flowchart.jpg',
      title: t('cases.caseStudies.electronicsManufacturer.title'),
      subtitle: t('cases.caseStudies.electronicsManufacturer.subtitle'),
      challenge: t('cases.caseStudies.electronicsManufacturer.challenge'),
      solution: t('cases.caseStudies.electronicsManufacturer.solution'),
      results: t('cases.caseStudies.electronicsManufacturer.results', { returnObjects: true }),
      metrics: [
        { label: t('cases.caseStudies.electronicsManufacturer.metrics.0.label'), value: t('cases.caseStudies.electronicsManufacturer.metrics.0.value'), icon: Award },
        { label: t('cases.caseStudies.electronicsManufacturer.metrics.1.label'), value: t('cases.caseStudies.electronicsManufacturer.metrics.1.value'), icon: TrendingUp },
        { label: t('cases.caseStudies.electronicsManufacturer.metrics.2.label'), value: t('cases.caseStudies.electronicsManufacturer.metrics.2.value'), icon: CheckCircle },
        { label: t('cases.caseStudies.electronicsManufacturer.metrics.3.label'), value: t('cases.caseStudies.electronicsManufacturer.metrics.3.value'), icon: Clock }
      ],
      testimonial: {
        quote: t('cases.caseStudies.electronicsManufacturer.testimonial.quote'),
        author: t('cases.caseStudies.electronicsManufacturer.testimonial.author'),
        position: t('cases.caseStudies.electronicsManufacturer.testimonial.position')
      },
      implementation: {
        duration: t('cases.caseStudies.electronicsManufacturer.implementation.duration'),
        scope: t('cases.caseStudies.electronicsManufacturer.implementation.scope'),
        team: t('cases.caseStudies.electronicsManufacturer.implementation.team')
      }
    },
    {
      id: 'commercial-bank',
      company: t('cases.caseStudies.commercialBank.company'),
      industry: 'financial',
      logo: '/images/business-intelligence.jpg',
      title: t('cases.caseStudies.commercialBank.title'),
      subtitle: t('cases.caseStudies.commercialBank.subtitle'),
      challenge: t('cases.caseStudies.commercialBank.challenge'),
      solution: t('cases.caseStudies.commercialBank.solution'),
      results: t('cases.caseStudies.commercialBank.results', { returnObjects: true }),
      metrics: [
        { label: t('cases.caseStudies.commercialBank.metrics.0.label'), value: t('cases.caseStudies.commercialBank.metrics.0.value'), icon: Clock },
        { label: t('cases.caseStudies.commercialBank.metrics.1.label'), value: t('cases.caseStudies.commercialBank.metrics.1.value'), icon: Shield },
        { label: t('cases.caseStudies.commercialBank.metrics.2.label'), value: t('cases.caseStudies.commercialBank.metrics.2.value'), icon: Users },
        { label: t('cases.caseStudies.commercialBank.metrics.3.label'), value: t('cases.caseStudies.commercialBank.metrics.3.value'), icon: TrendingUp }
      ],
      testimonial: {
        quote: t('cases.caseStudies.commercialBank.testimonial.quote'),
        author: t('cases.caseStudies.commercialBank.testimonial.author'),
        position: t('cases.caseStudies.commercialBank.testimonial.position')
      },
      implementation: {
        duration: t('cases.caseStudies.commercialBank.implementation.duration'),
        scope: t('cases.caseStudies.commercialBank.implementation.scope'),
        team: t('cases.caseStudies.commercialBank.implementation.team')
      }
    },
    {
      id: 'property-developer',
      company: t('cases.caseStudies.propertyDeveloper.company'),
      industry: 'real-estate',
      logo: '/images/executive-meeting.jpg',
      title: t('cases.caseStudies.propertyDeveloper.title'),
      subtitle: t('cases.caseStudies.propertyDeveloper.subtitle'),
      challenge: t('cases.caseStudies.propertyDeveloper.challenge'),
      solution: t('cases.caseStudies.propertyDeveloper.solution'),
      results: t('cases.caseStudies.propertyDeveloper.results', { returnObjects: true }),
      metrics: [
        { label: t('cases.caseStudies.propertyDeveloper.metrics.0.label'), value: t('cases.caseStudies.propertyDeveloper.metrics.0.value'), icon: Clock },
        { label: t('cases.caseStudies.propertyDeveloper.metrics.1.label'), value: t('cases.caseStudies.propertyDeveloper.metrics.1.value'), icon: TrendingUp },
        { label: t('cases.caseStudies.propertyDeveloper.metrics.2.label'), value: t('cases.caseStudies.propertyDeveloper.metrics.2.value'), icon: DollarSign },
        { label: t('cases.caseStudies.propertyDeveloper.metrics.3.label'), value: t('cases.caseStudies.propertyDeveloper.metrics.3.value'), icon: Users }
      ],
      testimonial: {
        quote: t('cases.caseStudies.propertyDeveloper.testimonial.quote'),
        author: t('cases.caseStudies.propertyDeveloper.testimonial.author'),
        position: t('cases.caseStudies.propertyDeveloper.testimonial.position')
      },
      implementation: {
        duration: t('cases.caseStudies.propertyDeveloper.implementation.duration'),
        scope: t('cases.caseStudies.propertyDeveloper.implementation.scope'),
        team: t('cases.caseStudies.propertyDeveloper.implementation.team')
      }
    },
    {
      id: 'pharma-company',
      company: t('cases.caseStudies.pharmaCompany.company'),
      industry: 'pharma',
      logo: '/images/data-analytics.jpg',
      title: t('cases.caseStudies.pharmaCompany.title'),
      subtitle: t('cases.caseStudies.pharmaCompany.subtitle'),
      challenge: t('cases.caseStudies.pharmaCompany.challenge'),
      solution: t('cases.caseStudies.pharmaCompany.solution'),
      results: t('cases.caseStudies.pharmaCompany.results', { returnObjects: true }),
      metrics: [
        { label: t('cases.caseStudies.pharmaCompany.metrics.0.label'), value: t('cases.caseStudies.pharmaCompany.metrics.0.value'), icon: Clock },
        { label: t('cases.caseStudies.pharmaCompany.metrics.1.label'), value: t('cases.caseStudies.pharmaCompany.metrics.1.value'), icon: TrendingUp },
        { label: t('cases.caseStudies.pharmaCompany.metrics.2.label'), value: t('cases.caseStudies.pharmaCompany.metrics.2.value'), icon: Award },
        { label: t('cases.caseStudies.pharmaCompany.metrics.3.label'), value: t('cases.caseStudies.pharmaCompany.metrics.3.value'), icon: BarChart3 }
      ],
      testimonial: {
        quote: t('cases.caseStudies.pharmaCompany.testimonial.quote'),
        author: t('cases.caseStudies.pharmaCompany.testimonial.author'),
        position: t('cases.caseStudies.pharmaCompany.testimonial.position')
      },
      implementation: {
        duration: t('cases.caseStudies.pharmaCompany.implementation.duration'),
        scope: t('cases.caseStudies.pharmaCompany.implementation.scope'),
        team: t('cases.caseStudies.pharmaCompany.implementation.team')
      }
    }
  ];

  const industries = t('cases.industries', { returnObjects: true });

  const filteredCases = activeFilter === 'all'
    ? caseStudies
    : caseStudies.filter(case_ => case_.industry === activeFilter);

  const featuredCase = caseStudies[0]; // Chabaidao as featured

  return (
    <div className="space-y-0">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-6">
            <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
              {t('cases.hero.badge')}
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
              {t('cases.hero.title')}
            </h1>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto">
              {t('cases.hero.subtitle')}
            </p>
          </div>
        </div>
      </section>

      {/* Featured Case Study - Chabaidao */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <Badge variant="secondary" className="bg-blue-100 text-blue-800 mb-4">{t('cases.featured.badge')}</Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">{t('cases.featured.title')}</h2>
          </div>

          <Card className="overflow-hidden border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-white">
            <div className="grid grid-cols-1 lg:grid-cols-2">
              <div className="p-8 lg:p-12 space-y-8">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <img
                      src={featuredCase.logo}
                      alt={featuredCase.company}
                      className="w-16 h-16 rounded-lg object-cover"
                    />
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900">{featuredCase.company}</h3>
                      <p className="text-blue-600 font-medium">{featuredCase.subtitle}</p>
                    </div>
                  </div>
                  <p className="text-gray-600 text-lg leading-relaxed">
                    {featuredCase.challenge}
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {featuredCase.metrics.map((metric, index) => {
                    const IconComponent = metric.icon;
                    return (
                      <div key={index} className="text-center p-4 bg-white rounded-lg border">
                        <IconComponent className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                        <div className="text-2xl font-bold text-gray-900">{metric.value}</div>
                        <div className="text-sm text-gray-600">{metric.label}</div>
                      </div>
                    );
                  })}
                </div>

                <Card className="bg-blue-50 border-blue-200">
                  <CardContent className="p-6">
                    <Quote className="w-8 h-8 text-blue-600 mb-4" />
                    <p className="text-gray-700 italic mb-4">"{featuredCase.testimonial.quote}"</p>
                    <div className="text-sm">
                      <div className="font-semibold text-gray-900">{featuredCase.testimonial.author}</div>
                      <div className="text-gray-600">{featuredCase.testimonial.position}</div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="relative">
                <img
                  src={featuredCase.logo}
                  alt={featuredCase.title}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-blue-600 bg-opacity-20"></div>
                <div className="absolute bottom-6 left-6 right-6 bg-white p-4 rounded-lg">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-lg font-bold text-blue-600">{featuredCase.implementation.duration}</div>
                      <div className="text-xs text-gray-600">{t('cases.common.implementation')}</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-green-600">8000+</div>
                      <div className="text-xs text-gray-600">{t('cases.common.stores')}</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-purple-600">35%</div>
                      <div className="text-xs text-gray-600">{t('cases.common.costReduction')}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </section>

      {/* All Case Studies */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-8">
            <div className="text-center space-y-4">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                {t('cases.moreStories.title')}
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {t('cases.moreStories.subtitle')}
              </p>
            </div>

            {/* Industry Filter */}
            <div className="flex flex-wrap justify-center gap-2">
              {industries.map((industry) => (
                <Button
                  key={industry.id}
                  variant={activeFilter === industry.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setActiveFilter(industry.id)}
                  className={activeFilter === industry.id ? "bg-blue-600 hover:bg-blue-700" : ""}
                >
                  {industry.name}
                </Button>
              ))}
            </div>

            {/* Case Studies Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {filteredCases.slice(1).map((case_, index) => (
                <Card key={case_.id} className="group hover:shadow-lg transition-all duration-300">
                  <CardHeader className="space-y-4">
                    <div className="flex items-start space-x-4">
                      <img
                        src={case_.logo}
                        alt={case_.company}
                        className="w-12 h-12 rounded-lg object-cover"
                      />
                      <div className="flex-1">
                        <CardTitle className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                          {case_.company}
                        </CardTitle>
                        <p className="text-sm text-gray-600">{case_.subtitle}</p>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {industries.find(i => i.id === case_.industry)?.name}
                      </Badge>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-6">
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {case_.challenge}
                    </p>

                    <div className="grid grid-cols-2 gap-3">
                      {case_.metrics.slice(0, 4).map((metric, metricIndex) => {
                        const IconComponent = metric.icon;
                        return (
                          <div key={metricIndex} className="text-center p-3 bg-gray-50 rounded-lg">
                            <IconComponent className="w-5 h-5 text-blue-600 mx-auto mb-1" />
                            <div className="text-lg font-bold text-gray-900">{metric.value}</div>
                            <div className="text-xs text-gray-600">{metric.label}</div>
                          </div>
                        );
                      })}
                    </div>

                    <Card className="bg-blue-50 border-blue-200">
                      <CardContent className="p-4">
                        <Quote className="w-6 h-6 text-blue-600 mb-2" />
                        <p className="text-gray-700 text-sm italic mb-3">"{case_.testimonial.quote}"</p>
                        <div className="text-xs">
                          <div className="font-semibold text-gray-900">{case_.testimonial.author}</div>
                          <div className="text-gray-600">{case_.testimonial.position}</div>
                        </div>
                      </CardContent>
                    </Card>

                    <div className="flex justify-between items-center pt-2 border-t border-gray-200">
                      <div className="text-sm text-gray-600">
                        <Clock className="w-4 h-4 inline mr-1" />
                        {case_.implementation.duration}
                      </div>
                      <Button variant="outline" size="sm" className="text-blue-600 hover:bg-blue-50">
                        {t('cases.common.learnMore')}
                        <ArrowRight className="w-4 h-4 ml-1" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Success Metrics */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              {t('cases.successMetrics.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('cases.successMetrics.subtitle')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
              <CardContent className="space-y-4">
                <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto">
                  <TrendingUp className="w-6 h-6" />
                </div>
                <div className="text-3xl font-bold text-blue-600">35%</div>
                <div className="text-sm text-gray-700 font-medium">{t('cases.successMetrics.metrics.0.label')}</div>
              </CardContent>
            </Card>

            <Card className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100 border-green-200">
              <CardContent className="space-y-4">
                <div className="w-12 h-12 bg-green-600 text-white rounded-full flex items-center justify-center mx-auto">
                  <DollarSign className="w-6 h-6" />
                </div>
                <div className="text-3xl font-bold text-green-600">40%</div>
                <div className="text-sm text-gray-700 font-medium">{t('cases.successMetrics.metrics.1.label')}</div>
              </CardContent>
            </Card>

            <Card className="text-center p-6 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
              <CardContent className="space-y-4">
                <div className="w-12 h-12 bg-purple-600 text-white rounded-full flex items-center justify-center mx-auto">
                  <Clock className="w-6 h-6" />
                </div>
                <div className="text-3xl font-bold text-purple-600">60%</div>
                <div className="text-sm text-gray-700 font-medium">{t('cases.successMetrics.metrics.2.label')}</div>
              </CardContent>
            </Card>

            <Card className="text-center p-6 bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
              <CardContent className="space-y-4">
                <div className="w-12 h-12 bg-orange-600 text-white rounded-full flex items-center justify-center mx-auto">
                  <Users className="w-6 h-6" />
                </div>
                <div className="text-3xl font-bold text-orange-600">95%</div>
                <div className="text-sm text-gray-700 font-medium">{t('cases.successMetrics.metrics.3.label')}</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="space-y-8">
            <h2 className="text-3xl md:text-4xl font-bold">
              {t('cases.cta.title')}
            </h2>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              {t('cases.cta.subtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3" asChild>
                <Link to="/contact">
                  {t('cases.cta.scheduleConsultation')}
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3" asChild>
                <Link to="/products">{t('cases.cta.explorePlatform')}</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default CasesPage;
