import { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ShoppingCart, 
  Factory, 
  Building, 
  Home, 
  Heart,
  TrendingUp,
  Users,
  Clock,
  DollarSign,
  BarChart3,
  ArrowRight,
  Quote,
  CheckCircle,
  Award,
  Shield
} from 'lucide-react';

const CasesPage = () => {
  const [activeFilter, setActiveFilter] = useState('all');

  const caseStudies = [
    {
      id: 'chabaidao',
      company: 'Chabaidao (茶百道)',
      industry: 'retail',
      logo: '/images/team-collaboration.jpg',
      title: 'Hyperautomation Across 8000+ Tea Stores',
      subtitle: 'Leading bubble tea chain achieves operational excellence',
      challenge: 'Managing operations across 8000+ stores with consistent quality, compliance, and efficiency while rapid expansion.',
      solution: 'Implemented BPMAX comprehensive hyperautomation platform covering store operations, supply chain, quality control, and staff training.',
      results: [
        '35% reduction in operational overhead',
        '50% faster new store onboarding',
        '95% compliance rate across all locations',
        '40% improvement in operational efficiency'
      ],
      metrics: [
        { label: 'Stores Managed', value: '8000+', icon: ShoppingCart },
        { label: 'Cost Reduction', value: '35%', icon: DollarSign },
        { label: 'Onboarding Speed', value: '50%', icon: Clock },
        { label: 'Compliance Rate', value: '95%', icon: CheckCircle }
      ],
      testimonial: {
        quote: "BPMAX has transformed how we manage our nationwide operations. The platform's AI-driven automation allows us to maintain consistent quality and service standards across all 8000+ stores while significantly reducing operational costs.",
        author: "Operations Director",
        position: "Chabaidao"
      },
      implementation: {
        duration: '6 months',
        scope: 'Nationwide rollout',
        team: '12 stores pilot → Full deployment'
      }
    },
    {
      id: 'electronics-manufacturer',
      company: 'Leading Electronics Manufacturer',
      industry: 'manufacturing',
      logo: '/images/process-flowchart.jpg',
      title: 'Smart Manufacturing with AI-Driven Quality Control',
      subtitle: 'Electronics manufacturer achieves zero-defect production',
      challenge: 'Complex multi-stage production with strict quality requirements and high customer expectations for defect-free products.',
      solution: 'Deployed BPMAX for integrated production planning, quality management, and predictive maintenance across manufacturing facilities.',
      results: [
        '99.5% quality compliance achievement',
        '25% increase in production efficiency',
        '50% reduction in quality defects',
        '30% decrease in equipment downtime'
      ],
      metrics: [
        { label: 'Quality Rate', value: '99.5%', icon: Award },
        { label: 'Efficiency Gain', value: '25%', icon: TrendingUp },
        { label: 'Defect Reduction', value: '50%', icon: CheckCircle },
        { label: 'Downtime Reduction', value: '30%', icon: Clock }
      ],
      testimonial: {
        quote: "The integration of AI-powered process mining with our production systems has been game-changing. We can now predict and prevent quality issues before they occur, achieving near-perfect quality rates.",
        author: "Manufacturing Director",
        position: "Electronics Manufacturing Company"
      },
      implementation: {
        duration: '8 months',
        scope: '3 manufacturing facilities',
        team: 'Pilot facility → Phased rollout'
      }
    },
    {
      id: 'commercial-bank',
      company: 'Regional Commercial Bank',
      industry: 'financial',
      logo: '/images/business-intelligence.jpg',
      title: 'Digital Transformation in Financial Services',
      subtitle: 'Bank accelerates loan processing and enhances compliance',
      challenge: 'Manual loan approval processes taking weeks to complete, creating customer dissatisfaction and competitive disadvantage.',
      solution: 'Automated end-to-end loan processing workflows with integrated risk assessment, compliance checking, and decision management.',
      results: [
        'Reduced processing time from 3 weeks to 3 days',
        '70% faster loan processing overall',
        '99% regulatory compliance achievement',
        '60% improvement in customer satisfaction'
      ],
      metrics: [
        { label: 'Processing Speed', value: '90%', icon: Clock },
        { label: 'Compliance Rate', value: '99%', icon: Shield },
        { label: 'Customer Satisfaction', value: '60%', icon: Users },
        { label: 'Efficiency Gain', value: '70%', icon: TrendingUp }
      ],
      testimonial: {
        quote: "BPMAX has revolutionized our loan processing capabilities. What used to take weeks now takes days, and our compliance team has complete confidence in our automated processes.",
        author: "Chief Operating Officer",
        position: "Regional Commercial Bank"
      },
      implementation: {
        duration: '4 months',
        scope: 'Corporate and retail banking',
        team: 'Loan department → Full banking operations'
      }
    },
    {
      id: 'property-developer',
      company: 'National Property Developer',
      industry: 'real-estate',
      logo: '/images/executive-meeting.jpg',
      title: 'Project Management Excellence in Real Estate',
      subtitle: 'Developer delivers projects faster with improved quality',
      challenge: 'Managing complex development projects across multiple cities with inconsistent project delivery times and quality issues.',
      solution: 'Implemented BPMAX for comprehensive project management, sales automation, and customer relationship management.',
      results: [
        '20% faster project delivery',
        '35% increase in operational efficiency',
        '50% faster property sales cycles',
        '40% improvement in customer satisfaction'
      ],
      metrics: [
        { label: 'Delivery Speed', value: '20%', icon: Clock },
        { label: 'Efficiency Gain', value: '35%', icon: TrendingUp },
        { label: 'Sales Speed', value: '50%', icon: DollarSign },
        { label: 'Customer Satisfaction', value: '40%', icon: Users }
      ],
      testimonial: {
        quote: "BPMAX has standardized our project management processes across all locations. We now deliver projects consistently on time and with higher quality, regardless of location complexity.",
        author: "Project Management Director",
        position: "National Property Developer"
      },
      implementation: {
        duration: '5 months',
        scope: '12 cities, 50+ projects',
        team: 'Regional pilot → National deployment'
      }
    },
    {
      id: 'pharma-company',
      company: 'Pharmaceutical Company',
      industry: 'pharma',
      logo: '/images/data-analytics.jpg',
      title: 'Regulatory Compliance and Drug Development',
      subtitle: 'Pharma company accelerates time-to-market while ensuring compliance',
      challenge: 'Complex regulatory approval processes for new drugs causing significant delays in time-to-market and high compliance costs.',
      solution: 'Streamlined regulatory workflows with automated documentation, compliance tracking, and submission management using BPMAX.',
      results: [
        'Reduced drug approval time by 6 months',
        '60% faster regulatory submissions',
        '99.9% compliance rate achievement',
        '40% reduction in time-to-market'
      ],
      metrics: [
        { label: 'Time Savings', value: '6 mo', icon: Clock },
        { label: 'Submission Speed', value: '60%', icon: TrendingUp },
        { label: 'Compliance Rate', value: '99.9%', icon: Award },
        { label: 'Market Speed', value: '40%', icon: BarChart3 }
      ],
      testimonial: {
        quote: "The automation of our regulatory processes has been transformative. We can now bring life-saving drugs to market significantly faster while maintaining the highest compliance standards.",
        author: "Regulatory Affairs Director",
        position: "Pharmaceutical Company"
      },
      implementation: {
        duration: '7 months',
        scope: 'Global regulatory operations',
        team: 'Regulatory department → R&D integration'
      }
    }
  ];

  const industries = [
    { id: 'all', name: 'All Industries' },
    { id: 'retail', name: 'Retail & F&B' },
    { id: 'manufacturing', name: 'Manufacturing' },
    { id: 'financial', name: 'Financial Services' },
    { id: 'real-estate', name: 'Real Estate' },
    { id: 'pharma', name: 'Life Sciences' }
  ];

  const filteredCases = activeFilter === 'all' 
    ? caseStudies 
    : caseStudies.filter(case_ => case_.industry === activeFilter);

  const featuredCase = caseStudies[0]; // Chabaidao as featured

  return (
    <div className="space-y-0">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-6">
            <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
              Customer Success Stories
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
              Transforming Businesses Worldwide
            </h1>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto">
              Discover how leading enterprises across various industries have achieved remarkable results 
              with BPMAX hyperautomation platform.
            </p>
          </div>
        </div>
      </section>

      {/* Featured Case Study - Chabaidao */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <Badge variant="secondary" className="bg-blue-100 text-blue-800 mb-4">Featured Success Story</Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">Chabaidao: Industry-Leading Digital Transformation</h2>
          </div>

          <Card className="overflow-hidden border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-white">
            <div className="grid grid-cols-1 lg:grid-cols-2">
              <div className="p-8 lg:p-12 space-y-8">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <img 
                      src={featuredCase.logo} 
                      alt={featuredCase.company} 
                      className="w-16 h-16 rounded-lg object-cover"
                    />
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900">{featuredCase.company}</h3>
                      <p className="text-blue-600 font-medium">{featuredCase.subtitle}</p>
                    </div>
                  </div>
                  <p className="text-gray-600 text-lg leading-relaxed">
                    {featuredCase.challenge}
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {featuredCase.metrics.map((metric, index) => {
                    const IconComponent = metric.icon;
                    return (
                      <div key={index} className="text-center p-4 bg-white rounded-lg border">
                        <IconComponent className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                        <div className="text-2xl font-bold text-gray-900">{metric.value}</div>
                        <div className="text-sm text-gray-600">{metric.label}</div>
                      </div>
                    );
                  })}
                </div>

                <Card className="bg-blue-50 border-blue-200">
                  <CardContent className="p-6">
                    <Quote className="w-8 h-8 text-blue-600 mb-4" />
                    <p className="text-gray-700 italic mb-4">"{featuredCase.testimonial.quote}"</p>
                    <div className="text-sm">
                      <div className="font-semibold text-gray-900">{featuredCase.testimonial.author}</div>
                      <div className="text-gray-600">{featuredCase.testimonial.position}</div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="relative">
                <img 
                  src={featuredCase.logo} 
                  alt={featuredCase.title} 
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-blue-600 bg-opacity-20"></div>
                <div className="absolute bottom-6 left-6 right-6 bg-white p-4 rounded-lg">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-lg font-bold text-blue-600">{featuredCase.implementation.duration}</div>
                      <div className="text-xs text-gray-600">Implementation</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-green-600">8000+</div>
                      <div className="text-xs text-gray-600">Stores</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-purple-600">35%</div>
                      <div className="text-xs text-gray-600">Cost Reduction</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </section>

      {/* All Case Studies */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-8">
            <div className="text-center space-y-4">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                More Success Stories
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Explore how companies across different industries have transformed their operations
              </p>
            </div>

            {/* Industry Filter */}
            <div className="flex flex-wrap justify-center gap-2">
              {industries.map((industry) => (
                <Button
                  key={industry.id}
                  variant={activeFilter === industry.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setActiveFilter(industry.id)}
                  className={activeFilter === industry.id ? "bg-blue-600 hover:bg-blue-700" : ""}
                >
                  {industry.name}
                </Button>
              ))}
            </div>

            {/* Case Studies Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {filteredCases.slice(1).map((case_, index) => (
                <Card key={case_.id} className="group hover:shadow-lg transition-all duration-300">
                  <CardHeader className="space-y-4">
                    <div className="flex items-start space-x-4">
                      <img 
                        src={case_.logo} 
                        alt={case_.company} 
                        className="w-12 h-12 rounded-lg object-cover"
                      />
                      <div className="flex-1">
                        <CardTitle className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                          {case_.company}
                        </CardTitle>
                        <p className="text-sm text-gray-600">{case_.subtitle}</p>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {industries.find(i => i.id === case_.industry)?.name}
                      </Badge>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-6">
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {case_.challenge}
                    </p>

                    <div className="grid grid-cols-2 gap-3">
                      {case_.metrics.slice(0, 4).map((metric, metricIndex) => {
                        const IconComponent = metric.icon;
                        return (
                          <div key={metricIndex} className="text-center p-3 bg-gray-50 rounded-lg">
                            <IconComponent className="w-5 h-5 text-blue-600 mx-auto mb-1" />
                            <div className="text-lg font-bold text-gray-900">{metric.value}</div>
                            <div className="text-xs text-gray-600">{metric.label}</div>
                          </div>
                        );
                      })}
                    </div>

                    <Card className="bg-blue-50 border-blue-200">
                      <CardContent className="p-4">
                        <Quote className="w-6 h-6 text-blue-600 mb-2" />
                        <p className="text-gray-700 text-sm italic mb-3">"{case_.testimonial.quote}"</p>
                        <div className="text-xs">
                          <div className="font-semibold text-gray-900">{case_.testimonial.author}</div>
                          <div className="text-gray-600">{case_.testimonial.position}</div>
                        </div>
                      </CardContent>
                    </Card>

                    <div className="flex justify-between items-center pt-2 border-t border-gray-200">
                      <div className="text-sm text-gray-600">
                        <Clock className="w-4 h-4 inline mr-1" />
                        {case_.implementation.duration}
                      </div>
                      <Button variant="outline" size="sm" className="text-blue-600 hover:bg-blue-50">
                        Learn More
                        <ArrowRight className="w-4 h-4 ml-1" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Success Metrics */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Aggregate Success Metrics
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Combined results from all our customer implementations
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
              <CardContent className="space-y-4">
                <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto">
                  <TrendingUp className="w-6 h-6" />
                </div>
                <div className="text-3xl font-bold text-blue-600">35%</div>
                <div className="text-sm text-gray-700 font-medium">Average Efficiency Gain</div>
              </CardContent>
            </Card>

            <Card className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100 border-green-200">
              <CardContent className="space-y-4">
                <div className="w-12 h-12 bg-green-600 text-white rounded-full flex items-center justify-center mx-auto">
                  <DollarSign className="w-6 h-6" />
                </div>
                <div className="text-3xl font-bold text-green-600">40%</div>
                <div className="text-sm text-gray-700 font-medium">Average Cost Reduction</div>
              </CardContent>
            </Card>

            <Card className="text-center p-6 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
              <CardContent className="space-y-4">
                <div className="w-12 h-12 bg-purple-600 text-white rounded-full flex items-center justify-center mx-auto">
                  <Clock className="w-6 h-6" />
                </div>
                <div className="text-3xl font-bold text-purple-600">60%</div>
                <div className="text-sm text-gray-700 font-medium">Faster Implementation</div>
              </CardContent>
            </Card>

            <Card className="text-center p-6 bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
              <CardContent className="space-y-4">
                <div className="w-12 h-12 bg-orange-600 text-white rounded-full flex items-center justify-center mx-auto">
                  <Users className="w-6 h-6" />
                </div>
                <div className="text-3xl font-bold text-orange-600">95%</div>
                <div className="text-sm text-gray-700 font-medium">Customer Satisfaction</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="space-y-8">
            <h2 className="text-3xl md:text-4xl font-bold">
              Ready to Write Your Success Story?
            </h2>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Join the growing list of enterprises that have transformed their operations with BPMAX. 
              Let's discuss how we can help achieve your digital transformation goals.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3" asChild>
                <Link to="/contact">
                  Schedule Success Consultation
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3" asChild>
                <Link to="/products">Explore Platform</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default CasesPage;
