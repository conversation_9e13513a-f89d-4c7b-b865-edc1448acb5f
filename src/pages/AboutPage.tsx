import { <PERSON> } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Brain,
  Target,
  Users,
  Award,
  TrendingUp,
  Globe,
  Shield,
  Zap,
  ArrowRight,
  CheckCircle,
  Building2,
  Lightbulb,
  Heart
} from 'lucide-react';

const AboutPage = () => {
  const { t } = useTranslation();

  const values = [
    {
      icon: Lightbulb,
      title: t('about.values.items.0.title'),
      description: t('about.values.items.0.description')
    },
    {
      icon: Users,
      title: t('about.values.items.1.title'),
      description: t('about.values.items.1.description')
    },
    {
      icon: Shield,
      title: t('about.values.items.2.title'),
      description: t('about.values.items.2.description')
    },
    {
      icon: Heart,
      title: t('about.values.items.3.title'),
      description: t('about.values.items.3.description')
    }
  ];

  const timeline = Array.isArray(t('about.timeline.milestones', { returnObjects: true }))
    ? t('about.timeline.milestones', { returnObjects: true })
    : [];

  const statsData = t('about.stats.metrics', { returnObjects: true });
  const stats = Array.isArray(statsData) ? statsData.map((stat, index) => ({
    icon: [Users, Globe, Building2, Award][index],
    label: stat.label,
    value: stat.value
  })) : [];

  const leadershipData = t('about.leadership.team', { returnObjects: true });
  const leadership = Array.isArray(leadershipData) ? leadershipData.map((leader, index) => ({
    ...leader,
    image: ['/images/executive-meeting.jpg', '/images/team-collaboration.jpg', '/images/business-intelligence.jpg', '/images/data-analytics.jpg'][index]
  })) : [];

  return (
    <div className="space-y-0">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
                  {t('about.hero.badge')}
                </Badge>
                <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
                  {t('about.hero.title')}{' '}
                  <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    {t('about.hero.titleHighlight')}
                  </span>
                </h1>
                <p className="text-xl text-gray-600 leading-relaxed">
                  {t('about.hero.description')}
                </p>
              </div>

              <div className="space-y-4">
                {Array.isArray(t('about.hero.highlights', { returnObjects: true })) ?
                  t('about.hero.highlights', { returnObjects: true }).map((highlight, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <CheckCircle className="w-6 h-6 text-green-500" />
                      <span className="text-gray-700">{highlight}</span>
                    </div>
                  )) : null}
              </div>

              <Button className="bg-blue-600 hover:bg-blue-700" asChild>
                <Link to="/contact">
                  {t('about.hero.partnerWithUs')}
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>

            <div className="relative">
              <img
                src="/images/tech-innovation.jpeg"
                alt="Innovation and Technology"
                className="w-full h-auto rounded-2xl shadow-2xl"
              />
              <div className="absolute -bottom-6 -left-6 bg-white p-6 rounded-xl shadow-lg border">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Brain className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <div className="text-lg font-bold text-gray-900">{t('about.hero.aiPowered')}</div>
                    <div className="text-sm text-gray-600">{t('about.hero.nextGenAutomation')}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            <Card className="p-8 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
              <CardHeader className="space-y-4">
                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                  <Target className="w-6 h-6 text-white" />
                </div>
                <CardTitle className="text-2xl font-bold text-gray-900">{t('about.mission.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed">
                  {t('about.mission.description')}
                </p>
              </CardContent>
            </Card>

            <Card className="p-8 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
              <CardHeader className="space-y-4">
                <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-6 h-6 text-white" />
                </div>
                <CardTitle className="text-2xl font-bold text-gray-900">{t('about.vision.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed">
                  {t('about.vision.description')}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Company Stats */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              {t('about.stats.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('about.stats.subtitle')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <Card key={index} className="text-center p-6 hover:shadow-lg transition-all">
                  <CardContent className="space-y-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto">
                      <IconComponent className="w-6 h-6 text-blue-600" />
                    </div>
                    <div className="text-3xl font-bold text-gray-900">{stat.value}</div>
                    <div className="text-sm text-gray-600 font-medium">{stat.label}</div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Core Values */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              {t('about.values.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('about.values.subtitle')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => {
              const IconComponent = value.icon;
              return (
                <Card key={index} className="text-center p-6 hover:shadow-lg transition-all group">
                  <CardHeader className="space-y-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto group-hover:bg-blue-600 transition-colors">
                      <IconComponent className="w-6 h-6 text-blue-600 group-hover:text-white" />
                    </div>
                    <CardTitle className="text-lg font-semibold text-gray-900">{value.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 text-sm">{value.description}</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Company Timeline */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              {t('about.timeline.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('about.timeline.subtitle')}
            </p>
          </div>

          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-0.5 bg-blue-200"></div>
            <div className="space-y-12">
              {timeline.map((item, index) => (
                <div key={index} className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}>
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                    <Card className="p-6 hover:shadow-lg transition-all">
                      <CardContent className="space-y-2">
                        <Badge variant="outline" className="text-blue-600 border-blue-300">
                          {item.year}
                        </Badge>
                        <h3 className="text-lg font-semibold text-gray-900">{item.title}</h3>
                        <p className="text-gray-600 text-sm">{item.description}</p>
                      </CardContent>
                    </Card>
                  </div>
                  <div className="relative z-10 flex items-center justify-center w-4 h-4">
                    <div className="w-4 h-4 bg-blue-600 rounded-full border-4 border-white shadow-lg"></div>
                  </div>
                  <div className="w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Leadership Team */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              {t('about.leadership.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('about.leadership.subtitle')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {leadership.map((leader, index) => (
              <Card key={index} className="text-center p-6 hover:shadow-lg transition-all">
                <CardHeader className="space-y-4">
                  <img
                    src={leader.image}
                    alt={leader.name}
                    className="w-20 h-20 rounded-full mx-auto object-cover"
                  />
                  <div>
                    <CardTitle className="text-lg font-semibold text-gray-900">{leader.name}</CardTitle>
                    <p className="text-blue-600 text-sm font-medium">{leader.position}</p>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 text-sm leading-relaxed">{leader.bio}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Technology & Innovation */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                  {t('about.technology.title')}
                </h2>
                <p className="text-xl text-gray-600">
                  {t('about.technology.description')}
                </p>
              </div>

              <div className="space-y-6">
                {Array.isArray(t('about.technology.sections', { returnObjects: true })) ?
                  t('about.technology.sections', { returnObjects: true }).map((section, index) => (
                    <div key={index} className="space-y-3">
                      <h3 className="text-lg font-semibold text-gray-900">{section.title}</h3>
                      <p className="text-gray-600">{section.description}</p>
                    </div>
                  )) : null}
              </div>
            </div>

            <div className="relative">
              <img
                src="/images/digital-transformation.png"
                alt="Technology Innovation"
                className="w-full h-auto rounded-2xl shadow-xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="space-y-8">
            <h2 className="text-3xl md:text-4xl font-bold">
              {t('about.cta.title')}
            </h2>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              {t('about.cta.subtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3" asChild>
                <Link to="/contact">
                  {t('about.cta.getInTouch')}
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3" asChild>
                <Link to="/cases">{t('about.cta.viewSuccessStories')}</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AboutPage;
