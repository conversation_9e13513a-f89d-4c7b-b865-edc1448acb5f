import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Brain, 
  Target, 
  Users, 
  Award,
  TrendingUp,
  Globe,
  Shield,
  Zap,
  ArrowRight,
  CheckCircle,
  Building2,
  Lightbulb,
  Heart
} from 'lucide-react';

const AboutPage = () => {
  const values = [
    {
      icon: Lightbulb,
      title: 'Innovation First',
      description: 'We push the boundaries of what\'s possible with AI and automation technology.'
    },
    {
      icon: Users,
      title: 'Customer Success',
      description: 'Our customers\' success is our primary measure of achievement.'
    },
    {
      icon: Shield,
      title: 'Trust & Security',
      description: 'We build secure, reliable solutions that enterprises can depend on.'
    },
    {
      icon: Heart,
      title: 'Excellence',
      description: 'We strive for excellence in everything we do, from product to service.'
    }
  ];

  const timeline = [
    {
      year: '2018',
      title: 'Company Founded',
      description: 'BPMAX was established with a vision to revolutionize business process management through AI.'
    },
    {
      year: '2019',
      title: 'First Product Launch',
      description: 'Released our initial BPM platform with basic workflow automation capabilities.'
    },
    {
      year: '2020',
      title: 'AI Integration',
      description: 'Introduced AI-powered process mining and intelligent automation features.'
    },
    {
      year: '2021',
      title: 'Market Expansion',
      description: 'Expanded to serve major enterprise clients across various industries.'
    },
    {
      year: '2022',
      title: 'Hyperautomation Platform',
      description: 'Launched comprehensive hyperautomation platform with five core engines.'
    },
    {
      year: '2023',
      title: 'Global Recognition',
      description: 'Achieved significant market recognition and expanded international presence.'
    },
    {
      year: '2024',
      title: 'Industry Leadership',
      description: 'Established as a leading AI-driven hyperautomation platform in the market.'
    }
  ];

  const stats = [
    { icon: Users, label: 'Enterprise Customers', value: '500+' },
    { icon: Globe, label: 'Countries Served', value: '12+' },
    { icon: Building2, label: 'Processes Automated', value: '10K+' },
    { icon: Award, label: 'Industry Awards', value: '15+' }
  ];

  const leadership = [
    {
      name: 'Dr. Michael Chen',
      position: 'Chief Executive Officer',
      bio: 'Former VP of Enterprise Solutions at leading tech company. PhD in Computer Science with 15+ years in enterprise software.',
      image: '/images/executive-meeting.jpg'
    },
    {
      name: 'Sarah Johnson',
      position: 'Chief Technology Officer',
      bio: 'AI and machine learning expert with extensive experience in process automation and enterprise architecture.',
      image: '/images/team-collaboration.jpg'
    },
    {
      name: 'David Wang',
      position: 'Chief Operating Officer',
      bio: 'Operations specialist with deep understanding of enterprise business processes and digital transformation.',
      image: '/images/business-intelligence.jpg'
    },
    {
      name: 'Lisa Zhang',
      position: 'Chief Customer Officer',
      bio: 'Customer success leader focused on ensuring enterprise clients achieve maximum value from their investments.',
      image: '/images/data-analytics.jpg'
    }
  ];

  return (
    <div className="space-y-0">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
                  About BPMAX
                </Badge>
                <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
                  Pioneering the Future of{' '}
                  <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Enterprise Automation
                  </span>
                </h1>
                <p className="text-xl text-gray-600 leading-relaxed">
                  BPMAX is not just a technology company—we are enablers of digital transformation, 
                  helping enterprises build self-evolving organizations that thrive in an ever-changing world.
                </p>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-6 h-6 text-green-500" />
                  <span className="text-gray-700">Leading AI-driven hyperautomation platform</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-6 h-6 text-green-500" />
                  <span className="text-gray-700">Trusted by 500+ enterprise customers</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-6 h-6 text-green-500" />
                  <span className="text-gray-700">Proven track record of digital transformation success</span>
                </div>
              </div>

              <Button className="bg-blue-600 hover:bg-blue-700" asChild>
                <Link to="/contact">
                  Partner with Us
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>

            <div className="relative">
              <img 
                src="/images/tech-innovation.jpeg" 
                alt="Innovation and Technology" 
                className="w-full h-auto rounded-2xl shadow-2xl"
              />
              <div className="absolute -bottom-6 -left-6 bg-white p-6 rounded-xl shadow-lg border">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Brain className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <div className="text-lg font-bold text-gray-900">AI-Powered</div>
                    <div className="text-sm text-gray-600">Next-gen automation</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            <Card className="p-8 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
              <CardHeader className="space-y-4">
                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                  <Target className="w-6 h-6 text-white" />
                </div>
                <CardTitle className="text-2xl font-bold text-gray-900">Our Mission</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed">
                  To empower enterprises with AI-driven hyperautomation capabilities that enable them to build 
                  self-evolving digital twin organizations, achieve continuous transformation, and maintain 
                  competitive advantage in rapidly changing markets.
                </p>
              </CardContent>
            </Card>

            <Card className="p-8 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
              <CardHeader className="space-y-4">
                <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-6 h-6 text-white" />
                </div>
                <CardTitle className="text-2xl font-bold text-gray-900">Our Vision</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed">
                  To be the global leader in intelligent process automation, setting the standard for how 
                  enterprises leverage AI to create adaptive, efficient, and resilient business operations 
                  that drive sustainable growth and innovation.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Company Stats */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Trusted by Enterprises Worldwide
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our impact speaks through the success of our customers
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <Card key={index} className="text-center p-6 hover:shadow-lg transition-all">
                  <CardContent className="space-y-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto">
                      <IconComponent className="w-6 h-6 text-blue-600" />
                    </div>
                    <div className="text-3xl font-bold text-gray-900">{stat.value}</div>
                    <div className="text-sm text-gray-600 font-medium">{stat.label}</div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Core Values */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Our Core Values
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              The principles that guide everything we do
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => {
              const IconComponent = value.icon;
              return (
                <Card key={index} className="text-center p-6 hover:shadow-lg transition-all group">
                  <CardHeader className="space-y-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto group-hover:bg-blue-600 transition-colors">
                      <IconComponent className="w-6 h-6 text-blue-600 group-hover:text-white" />
                    </div>
                    <CardTitle className="text-lg font-semibold text-gray-900">{value.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 text-sm">{value.description}</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Company Timeline */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Our Journey
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Key milestones in our evolution as a leading hyperautomation platform
            </p>
          </div>

          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-0.5 bg-blue-200"></div>
            <div className="space-y-12">
              {timeline.map((item, index) => (
                <div key={index} className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}>
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                    <Card className="p-6 hover:shadow-lg transition-all">
                      <CardContent className="space-y-2">
                        <Badge variant="outline" className="text-blue-600 border-blue-300">
                          {item.year}
                        </Badge>
                        <h3 className="text-lg font-semibold text-gray-900">{item.title}</h3>
                        <p className="text-gray-600 text-sm">{item.description}</p>
                      </CardContent>
                    </Card>
                  </div>
                  <div className="relative z-10 flex items-center justify-center w-4 h-4">
                    <div className="w-4 h-4 bg-blue-600 rounded-full border-4 border-white shadow-lg"></div>
                  </div>
                  <div className="w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Leadership Team */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Leadership Team
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Experienced leaders driving innovation and customer success
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {leadership.map((leader, index) => (
              <Card key={index} className="text-center p-6 hover:shadow-lg transition-all">
                <CardHeader className="space-y-4">
                  <img 
                    src={leader.image} 
                    alt={leader.name} 
                    className="w-20 h-20 rounded-full mx-auto object-cover"
                  />
                  <div>
                    <CardTitle className="text-lg font-semibold text-gray-900">{leader.name}</CardTitle>
                    <p className="text-blue-600 text-sm font-medium">{leader.position}</p>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 text-sm leading-relaxed">{leader.bio}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Technology & Innovation */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                  Technology & Innovation
                </h2>
                <p className="text-xl text-gray-600">
                  At the forefront of AI and automation technology, we continuously innovate to deliver 
                  cutting-edge solutions that solve real business challenges.
                </p>
              </div>

              <div className="space-y-6">
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-gray-900">Research & Development</h3>
                  <p className="text-gray-600">
                    Our R&D team continuously explores emerging technologies in AI, machine learning, 
                    and process automation to enhance our platform capabilities.
                  </p>
                </div>

                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-gray-900">Industry Partnerships</h3>
                  <p className="text-gray-600">
                    We collaborate with leading technology companies and research institutions to 
                    stay at the forefront of innovation.
                  </p>
                </div>

                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-gray-900">Continuous Improvement</h3>
                  <p className="text-gray-600">
                    Customer feedback drives our product development, ensuring our solutions 
                    evolve to meet changing enterprise needs.
                  </p>
                </div>
              </div>
            </div>

            <div className="relative">
              <img 
                src="/images/digital-transformation.png" 
                alt="Technology Innovation" 
                className="w-full h-auto rounded-2xl shadow-xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="space-y-8">
            <h2 className="text-3xl md:text-4xl font-bold">
              Join Us in Shaping the Future
            </h2>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Whether you're looking to transform your business operations or join our team, 
              we'd love to hear from you.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3" asChild>
                <Link to="/contact">
                  Get in Touch
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3" asChild>
                <Link to="/cases">View Success Stories</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AboutPage;
