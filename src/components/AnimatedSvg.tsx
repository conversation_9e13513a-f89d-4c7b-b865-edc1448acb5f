import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

interface AnimatedSvgProps {
  className?: string;
  alt?: string;
}

const AnimatedSvg: React.FC<AnimatedSvgProps> = ({
  className = "w-full h-auto",
  alt = "BPMAX Hyperautomation Platform"
}) => {
  const { i18n } = useTranslation();
  const [visibleElements, setVisibleElements] = useState<number[]>([]);
  const [isAnimating, setIsAnimating] = useState(false);

  // 根据语言选择SVG文件
  const getSvgPath = () => {
    return i18n.language === 'en'
      ? '/images/bomaxdtoen.svg'
      : '/images/bpmaxdto.svg';
  };

  // 动画步骤定义 - 元素依次出现
  const animationSteps = [
    { elementIndex: 0, delay: 0 },      // 第一个元素
    { elementIndex: 1, delay: 500 },    // 第二个元素
    { elementIndex: 2, delay: 1000 },   // 第三个元素
    { elementIndex: 3, delay: 1500 },   // 第四个元素
    { elementIndex: 4, delay: 2000 },   // 第五个元素
    { elementIndex: 5, delay: 2500 },   // 第六个元素
  ];

  // 启动动画
  const startAnimation = () => {
    setIsAnimating(true);
    setVisibleElements([]);

    animationSteps.forEach((step, index) => {
      setTimeout(() => {
        setVisibleElements(prev => [...prev, step.elementIndex]);
        if (index === animationSteps.length - 1) {
          setTimeout(() => setIsAnimating(false), 300);
        }
      }, step.delay);
    });
  };

  // 组件挂载时启动动画
  useEffect(() => {
    const timer = setTimeout(() => {
      startAnimation();
    }, 1200); // 延迟1.2秒开始，让页面其他元素先加载

    return () => clearTimeout(timer);
  }, []);

  // 语言切换时重新启动动画
  useEffect(() => {
    if (!isAnimating) {
      const timer = setTimeout(() => {
        startAnimation();
      }, 200);
      return () => clearTimeout(timer);
    }
  }, [i18n.language]);

  return (
    <div className="relative">
      <div className="relative">
        {/* 创建多个图层，每个代表一个元素 */}
        {animationSteps.map((step, index) => (
          <div
            key={index}
            className="absolute inset-0"
            style={{
              opacity: visibleElements.includes(step.elementIndex) ? 1 : 0,
              transform: visibleElements.includes(step.elementIndex)
                ? 'translateY(0) scale(1)'
                : 'translateY(15px) scale(0.98)',
              transition: 'all 0.8s ease-out',
              zIndex: index + 1,
            }}
          >
            <img
              src={getSvgPath()}
              alt={alt}
              className={className}
              style={{
                filter: `drop-shadow(0 2px 8px rgba(116, 77, 209, ${0.08 + index * 0.01}))`,
                clipPath: getElementClipPath(step.elementIndex),
              }}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

// 根据元素索引生成裁剪路径，让不同部分的元素依次显示
const getElementClipPath = (elementIndex: number): string => {
  switch (elementIndex) {
    case 0:
      // 显示左上角区域 - 组织建模
      return 'polygon(0% 0%, 60% 0%, 60% 60%, 0% 60%)';
    case 1:
      // 显示右上角区域 - 数据导入
      return 'polygon(40% 0%, 100% 0%, 100% 60%, 40% 60%)';
    case 2:
      // 显示中心区域 - 业务分析
      return 'polygon(20% 20%, 80% 20%, 80% 80%, 20% 80%)';
    case 3:
      // 显示左下角区域 - 模型改进
      return 'polygon(0% 40%, 60% 40%, 60% 100%, 0% 100%)';
    case 4:
      // 显示右下角区域 - 组织改进
      return 'polygon(40% 40%, 100% 40%, 100% 100%, 40% 100%)';
    case 5:
      // 显示连接线和箭头 - 完整显示
      return 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)';
    default:
      return 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)';
  }
};

export default AnimatedSvg;
