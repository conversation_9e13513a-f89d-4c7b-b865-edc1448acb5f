import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

interface AnimatedSvgProps {
  className?: string;
  alt?: string;
}

const AnimatedSvg: React.FC<AnimatedSvgProps> = ({
  className = "w-full h-auto",
  alt = "BPMAX Hyperautomation Platform"
}) => {
  const { i18n } = useTranslation();
  const [isHovered, setIsHovered] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language || 'en');
  const [tiltValues, setTiltValues] = useState({ rotateX: 0, rotateY: 0 });
  
  const containerRef = useRef<HTMLDivElement>(null);
  const animationFrameRef = useRef<number>();
  const lastMousePositionRef = useRef({ x: 0, y: 0 });
  const boundingRectRef = useRef<DOMRect | null>(null);
  const isAnimatingRef = useRef(false);

  // 监听语言变化
  useEffect(() => {
    setCurrentLanguage(i18n.language || 'en');
  }, [i18n.language]);

  // 缓存SVG路径，避免重复计算
  const svgPath = useMemo(() => {
    const lang = currentLanguage || 'en';
    return lang.startsWith('zh') 
      ? '/images/bpmaxdto.svg'
      : '/images/bomaxdtoen.svg';
  }, [currentLanguage]);

  // 优化的倾斜计算函数，使用缓存的边界矩形
  const calculateTilt = useCallback((mouseX: number, mouseY: number) => {
    if (!boundingRectRef.current || !isHovered) {
      return { rotateX: 0, rotateY: 0 };
    }

    const rect = boundingRectRef.current;
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    // 计算鼠标相对于中心的位置
    const deltaX = mouseX - centerX;
    const deltaY = mouseY - centerY;

    // 计算倾斜角度（限制在-12到12度之间，减少过度倾斜）
    const maxTilt = 12;
    const rotateY = Math.max(-maxTilt, Math.min(maxTilt, (deltaX / (rect.width / 2)) * maxTilt));
    const rotateX = Math.max(-maxTilt, Math.min(maxTilt, -(deltaY / (rect.height / 2)) * maxTilt));

    return { rotateX, rotateY };
  }, [isHovered]);

  // 使用requestAnimationFrame优化动画更新
  const updateTilt = useCallback(() => {
    if (!isAnimatingRef.current) return;

    const { x, y } = lastMousePositionRef.current;
    const newTiltValues = calculateTilt(x, y);
    
    setTiltValues(prevValues => {
      // 只有当值发生显著变化时才更新，减少不必要的重渲染
      const threshold = 0.1;
      if (
        Math.abs(prevValues.rotateX - newTiltValues.rotateX) > threshold ||
        Math.abs(prevValues.rotateY - newTiltValues.rotateY) > threshold
      ) {
        return newTiltValues;
      }
      return prevValues;
    });

    if (isAnimatingRef.current) {
      animationFrameRef.current = requestAnimationFrame(updateTilt);
    }
  }, [calculateTilt]);

  // 节流的鼠标移动处理
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    lastMousePositionRef.current = { x: e.clientX, y: e.clientY };
    
    if (!isAnimatingRef.current && isHovered) {
      isAnimatingRef.current = true;
      animationFrameRef.current = requestAnimationFrame(updateTilt);
    }
  }, [isHovered, updateTilt]);

  // 鼠标进入处理
  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
    
    // 缓存边界矩形，避免重复计算
    if (containerRef.current) {
      boundingRectRef.current = containerRef.current.getBoundingClientRect();
    }
    
    isAnimatingRef.current = true;
    animationFrameRef.current = requestAnimationFrame(updateTilt);
  }, [updateTilt]);

  // 鼠标离开处理
  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
    isAnimatingRef.current = false;
    
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    
    // 平滑重置到初始状态
    setTiltValues({ rotateX: 0, rotateY: 0 });
  }, []);

  // 清理动画帧
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  // 窗口大小变化时更新边界矩形缓存
  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current && isHovered) {
        boundingRectRef.current = containerRef.current.getBoundingClientRect();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isHovered]);

  // 优化的样式对象，使用useMemo避免重复创建
  const containerStyle = useMemo(() => ({
    perspective: '1000px',
    transformStyle: 'preserve-3d' as const
  }), []);

  const imageStyle = useMemo(() => ({
    filter: isHovered 
      ? 'drop-shadow(0 12px 30px rgba(116, 77, 209, 0.25))' 
      : 'drop-shadow(0 8px 20px rgba(116, 77, 209, 0.15))',
    transform: isHovered 
      ? `scale(1.02) rotateX(${tiltValues.rotateX.toFixed(2)}deg) rotateY(${tiltValues.rotateY.toFixed(2)}deg)` 
      : 'scale(1) rotateX(0deg) rotateY(0deg)',
    transition: isHovered 
      ? 'filter 0.3s ease-out' // 悬停时移除transform过渡，让动画更流畅
      : 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)', // 离开时使用过渡
    transformOrigin: 'center center',
    willChange: isHovered ? 'transform' : 'auto', // 优化GPU加速
  }), [isHovered, tiltValues.rotateX, tiltValues.rotateY]);

  return (
    <div
      ref={containerRef}
      className="relative cursor-pointer"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseMove={handleMouseMove}
      style={containerStyle}
    >
      <img
        src={svgPath}
        alt={alt}
        className={className}
        style={imageStyle}
      />
    </div>
  );
};

export default AnimatedSvg;
