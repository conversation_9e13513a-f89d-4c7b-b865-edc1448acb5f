import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

interface AnimatedSvgProps {
  className?: string;
  alt?: string;
}

const AnimatedSvg: React.FC<AnimatedSvgProps> = ({
  className = "w-full h-auto",
  alt = "BPMAX Hyperautomation Platform"
}) => {
  const { i18n } = useTranslation();
  const [isHovered, setIsHovered] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language || 'en');

  // 监听语言变化
  useEffect(() => {
    setCurrentLanguage(i18n.language || 'en');
  }, [i18n.language]);

  // 根据语言选择SVG文件
  const getSvgPath = () => {
    // 确保使用当前语言状态，并且默认为英文
    const lang = currentLanguage || 'en';
    return lang.startsWith('zh') 
      ? '/images/bpmaxdto.svg'
      : '/images/bomaxdtoen.svg';
  };

  // 鼠标悬停处理
  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  return (
    <div 
      className="relative cursor-pointer"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <img
        src={getSvgPath()}
        alt={alt}
        className={className}
        style={{
          filter: isHovered 
            ? 'drop-shadow(0 12px 30px rgba(116, 77, 209, 0.25))' 
            : 'drop-shadow(0 8px 20px rgba(116, 77, 209, 0.15))',
          transform: isHovered ? 'scale(1.02)' : 'scale(1)',
          transition: 'all 0.3s ease-out',
        }}
      />
    </div>
  );
};

export default AnimatedSvg;
