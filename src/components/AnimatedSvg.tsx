import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

interface AnimatedSvgProps {
  className?: string;
  alt?: string;
}

const AnimatedSvg: React.FC<AnimatedSvgProps> = ({
  className = "w-full h-auto",
  alt = "BPMAX Hyperautomation Platform"
}) => {
  const { i18n } = useTranslation();
  const [currentStep, setCurrentStep] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language || 'en');

  // 监听语言变化
  useEffect(() => {
    setCurrentLanguage(i18n.language || 'en');
  }, [i18n.language]);

  // 根据语言选择SVG文件
  const getSvgPath = () => {
    // 确保使用当前语言状态，并且默认为英文
    const lang = currentLanguage || 'en';
    return lang.startsWith('zh') 
      ? '/images/bpmaxdto.svg'
      : '/images/bomaxdtoen.svg';
  };

  // 动画步骤定义 - 简化版本
  const animationSteps = [
    { delay: 0 },      // 开始
    { delay: 600 },    // 第一步
    { delay: 1200 },   // 第二步
    { delay: 1800 },   // 第三步
    { delay: 2400 },   // 第四步
    { delay: 3000 },   // 完成
  ];

  // 启动动画
  const startAnimation = () => {
    setIsAnimating(true);
    setCurrentStep(0);

    animationSteps.forEach((step, index) => {
      setTimeout(() => {
        setCurrentStep(index + 1);
        if (index === animationSteps.length - 1) {
          setTimeout(() => setIsAnimating(false), 300);
        }
      }, step.delay);
    });
  };

  // 组件挂载时启动动画
  useEffect(() => {
    const timer = setTimeout(() => {
      startAnimation();
    }, 1000); // 延迟1秒开始

    return () => clearTimeout(timer);
  }, []);

  // 语言切换时重新启动动画
  useEffect(() => {
    if (!isAnimating) {
      const timer = setTimeout(() => {
        startAnimation();
      }, 200);
      return () => clearTimeout(timer);
    }
  }, [currentLanguage, isAnimating]);

  return (
    <div className="relative">
      <img
        src={getSvgPath()}
        alt={alt}
        className={className}
        style={{
          filter: 'drop-shadow(0 8px 20px rgba(116, 77, 209, 0.15))',
          clipPath: getClipPath(currentStep),
          transition: 'clip-path 0.8s ease-out',
        }}
      />
    </div>
  );
};

// 根据当前步骤生成裁剪路径 - 渐进式显示
const getClipPath = (step: number): string => {
  switch (step) {
    case 0:
      return 'circle(0% at 50% 50%)'; // 完全隐藏
    case 1:
      // 显示中心小圆
      return 'circle(15% at 50% 50%)';
    case 2:
      // 扩展到中等大小
      return 'circle(35% at 50% 50%)';
    case 3:
      // 继续扩展
      return 'circle(55% at 50% 50%)';
    case 4:
      // 几乎全部显示
      return 'circle(75% at 50% 50%)';
    case 5:
      // 完全显示
      return 'circle(100% at 50% 50%)';
    default:
      return 'circle(100% at 50% 50%)';
  }
};

export default AnimatedSvg;
