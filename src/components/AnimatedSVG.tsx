import React, { useEffect, useRef } from 'react';

interface AnimatedSVGProps {
  src: string;
  alt: string;
  className?: string;
}

const AnimatedSVG: React.FC<AnimatedSVGProps> = ({ src, alt, className = '' }) => {
  const svgRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const loadSVG = async () => {
      try {
        const response = await fetch(src);
        const svgText = await response.text();
        
        if (svgRef.current) {
          svgRef.current.innerHTML = svgText;
          
          // 获取SVG元素
          const svgElement = svgRef.current.querySelector('svg');
          if (svgElement) {
            // 添加动画类
            svgElement.classList.add('w-full', 'h-auto');
            
            // 为SVG内的元素添加动画
            const circles = svgElement.querySelectorAll('circle, ellipse');
            const paths = svgElement.querySelectorAll('path');
            const groups = svgElement.querySelectorAll('g');
            
            // 为圆形元素添加脉冲动画
            circles.forEach((circle, index) => {
              circle.setAttribute('style', `
                animation: pulse 2s ease-in-out infinite;
                animation-delay: ${index * 0.2}s;
                transform-origin: center;
              `);
            });
            
            // 为路径添加绘制动画
            paths.forEach((path, index) => {
              const pathLength = (path as SVGPathElement).getTotalLength?.() || 0;
              if (pathLength > 0) {
                path.setAttribute('style', `
                  stroke-dasharray: ${pathLength};
                  stroke-dashoffset: ${pathLength};
                  animation: drawPath 3s ease-in-out forwards;
                  animation-delay: ${index * 0.1}s;
                `);
              }
            });
            
            // 为组添加淡入动画
            groups.forEach((group, index) => {
              group.setAttribute('style', `
                opacity: 0;
                animation: fadeInScale 1s ease-out forwards;
                animation-delay: ${index * 0.3}s;
              `);
            });
            
            // 添加CSS动画定义
            const style = document.createElement('style');
            style.textContent = `
              @keyframes drawPath {
                to {
                  stroke-dashoffset: 0;
                }
              }
              
              @keyframes fadeInScale {
                0% {
                  opacity: 0;
                  transform: scale(0.8);
                }
                100% {
                  opacity: 1;
                  transform: scale(1);
                }
              }
              
              @keyframes pulse {
                0%, 100% {
                  transform: scale(1);
                  opacity: 1;
                }
                50% {
                  transform: scale(1.05);
                  opacity: 0.8;
                }
              }
              
              @keyframes rotate {
                from {
                  transform: rotate(0deg);
                }
                to {
                  transform: rotate(360deg);
                }
              }
              
              .svg-hover-effect:hover circle,
              .svg-hover-effect:hover ellipse {
                animation: pulse 1s ease-in-out infinite;
              }
              
              .svg-hover-effect:hover path {
                filter: drop-shadow(0 0 8px rgba(147, 51, 234, 0.4));
              }
            `;
            
            if (!document.head.querySelector('#svg-animations')) {
              style.id = 'svg-animations';
              document.head.appendChild(style);
            }
            
            // 添加悬停效果类
            svgElement.classList.add('svg-hover-effect');
          }
        }
      } catch (error) {
        console.error('Error loading SVG:', error);
        // 如果加载失败，显示普通图片
        if (svgRef.current) {
          svgRef.current.innerHTML = `<img src="${src}" alt="${alt}" class="${className}" />`;
        }
      }
    };

    loadSVG();
  }, [src, alt, className]);

  return (
    <div 
      ref={svgRef} 
      className={`svg-container ${className}`}
      role="img"
      aria-label={alt}
    />
  );
};

export default AnimatedSVG;
