import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

interface AnimatedSvgProps {
  className?: string;
  alt?: string;
}

const AnimatedSvg: React.FC<AnimatedSvgProps> = ({ 
  className = "w-full h-auto", 
  alt = "BPMAX Hyperautomation Platform" 
}) => {
  const { i18n } = useTranslation();
  const [currentStep, setCurrentStep] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  // 根据语言选择SVG文件
  const getSvgPath = () => {
    return i18n.language === 'en' 
      ? '/images/bomaxdtoen.svg' 
      : '/images/bpmaxdto.svg';
  };

  // 动画步骤定义 - 快速丝滑
  const animationSteps = [
    { name: 'organization', delay: 0 },      // 组织建模
    { name: 'dataImport', delay: 300 },     // 数据导入
    { name: 'analysis', delay: 600 },       // 业务挖掘和分析
    { name: 'simulation', delay: 900 },     // 模型改进和仿真
    { name: 'improvement', delay: 1200 },   // 组织改进
    { name: 'arrows', delay: 1500 },        // 箭头连接
  ];

  // 启动动画
  const startAnimation = () => {
    setIsAnimating(true);
    setCurrentStep(0);
    
    animationSteps.forEach((step, index) => {
      setTimeout(() => {
        setCurrentStep(index + 1);
        if (index === animationSteps.length - 1) {
          setTimeout(() => setIsAnimating(false), 300);
        }
      }, step.delay);
    });
  };

  // 组件挂载时启动动画
  useEffect(() => {
    const timer = setTimeout(() => {
      startAnimation();
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  // 语言切换时重新启动动画
  useEffect(() => {
    if (!isAnimating) {
      const timer = setTimeout(() => {
        startAnimation();
      }, 200);
      return () => clearTimeout(timer);
    }
  }, [i18n.language]);

  return (
    <div className="relative">
      <div className="relative overflow-hidden">
        <img
          src={getSvgPath()}
          alt={alt}
          className={`${className} transition-opacity duration-300`}
          style={{
            opacity: 0.08, // 背景图片几乎透明
          }}
        />
        
        {/* 动画遮罩层 */}
        <div className="absolute inset-0">
          <img
            src={getSvgPath()}
            alt={alt}
            className={`${className} transition-all duration-300 ease-out`}
            style={{
              clipPath: getClipPath(currentStep),
              filter: 'drop-shadow(0 8px 20px rgba(116, 77, 209, 0.15))',
            }}
          />
        </div>
      </div>
    </div>
  );
};

// 根据当前步骤生成裁剪路径
const getClipPath = (step: number): string => {
  switch (step) {
    case 0:
      return 'circle(0% at 50% 50%)'; // 完全隐藏
    case 1:
      // 组织建模 - 从左上角开始显示
      return 'circle(20% at 25% 25%)';
    case 2:
      // 数据导入 - 扩展到更多区域
      return 'circle(35% at 40% 35%)';
    case 3:
      // 业务挖掘和分析 - 显示中心区域
      return 'circle(50% at 50% 50%)';
    case 4:
      // 模型改进和仿真 - 扩展到右侧
      return 'circle(65% at 60% 55%)';
    case 5:
      // 组织改进 - 几乎全部显示
      return 'circle(80% at 50% 50%)';
    case 6:
      // 显示全部包括箭头连接线
      return 'circle(100% at 50% 50%)';
    default:
      return 'circle(100% at 50% 50%)';
  }
};

export default AnimatedSvg;
