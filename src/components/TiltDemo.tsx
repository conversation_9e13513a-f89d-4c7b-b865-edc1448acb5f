import React from 'react';
import AnimatedSvg from './AnimatedSvg';

const TiltDemo: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-8">
      <div className="max-w-4xl w-full">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            鼠标跟随倾斜效果演示
          </h1>
          <p className="text-gray-300 text-lg">
            将鼠标悬停在图片上，移动鼠标查看3D倾斜效果
          </p>
        </div>
        
        <div className="flex justify-center">
          <div className="w-full max-w-2xl">
            <AnimatedSvg className="w-full h-auto" />
          </div>
        </div>
        
        <div className="text-center mt-8">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-white">
            <h3 className="text-xl font-semibold mb-3">效果特点：</h3>
            <ul className="text-left space-y-2 max-w-md mx-auto">
              <li>• 鼠标悬停时激活3D倾斜效果</li>
              <li>• 根据鼠标位置实时计算倾斜角度</li>
              <li>• 平滑的过渡动画</li>
              <li>• 增强的阴影效果</li>
              <li>• 支持中英文语言切换</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TiltDemo;
