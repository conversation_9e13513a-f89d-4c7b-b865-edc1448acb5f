import { Link } from 'react-router-dom';
import { Brain, Mail, Phone, MapPin, Linkedin, Twitter, Youtube } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg flex items-center justify-center">
                <Brain className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold">BPMAX</span>
            </div>
            <p className="text-gray-300 text-sm">
              AI-Powered Enterprise Hyperautomation Platform. Transforming businesses through intelligent process automation and digital twin organizations.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Linkedin className="w-5 h-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Twitter className="w-5 h-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Youtube className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Products */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Products</h3>
            <ul className="space-y-2 text-sm">
              <li><Link to="/products#ai-engine" className="text-gray-300 hover:text-white transition-colors">AI Process Engine</Link></li>
              <li><Link to="/products#form-engine" className="text-gray-300 hover:text-white transition-colors">Form Engine</Link></li>
              <li><Link to="/products#data-engine" className="text-gray-300 hover:text-white transition-colors">Data Engine</Link></li>
              <li><Link to="/products#org-modeling" className="text-gray-300 hover:text-white transition-colors">Organization Modeling</Link></li>
              <li><Link to="/products#process-mining" className="text-gray-300 hover:text-white transition-colors">Process Mining & Simulation</Link></li>
            </ul>
          </div>

          {/* Solutions */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Solutions</h3>
            <ul className="space-y-2 text-sm">
              <li><Link to="/solutions#retail" className="text-gray-300 hover:text-white transition-colors">Retail & F&B</Link></li>
              <li><Link to="/solutions#manufacturing" className="text-gray-300 hover:text-white transition-colors">Manufacturing</Link></li>
              <li><Link to="/solutions#financial" className="text-gray-300 hover:text-white transition-colors">Financial Services</Link></li>
              <li><Link to="/solutions#real-estate" className="text-gray-300 hover:text-white transition-colors">Real Estate</Link></li>
              <li><Link to="/solutions#pharma" className="text-gray-300 hover:text-white transition-colors">Life Sciences</Link></li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact Us</h3>
            <ul className="space-y-3 text-sm">
              <li className="flex items-center space-x-2">
                <Phone className="w-4 h-4 text-gray-400" />
                <span className="text-gray-300">+86 ************</span>
              </li>
              <li className="flex items-center space-x-2">
                <Mail className="w-4 h-4 text-gray-400" />
                <span className="text-gray-300"><EMAIL></span>
              </li>
              <li className="flex items-start space-x-2">
                <MapPin className="w-4 h-4 text-gray-400 mt-0.5" />
                <span className="text-gray-300">
                  Building A, TechHub District<br />
                  Beijing, China 100000
                </span>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <div className="text-sm text-gray-400">
            © 2025 BPMAX Technology Co., Ltd. All rights reserved.
          </div>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <Link to="/privacy" className="text-sm text-gray-400 hover:text-white transition-colors">Privacy Policy</Link>
            <Link to="/terms" className="text-sm text-gray-400 hover:text-white transition-colors">Terms of Service</Link>
            <Link to="/cookies" className="text-sm text-gray-400 hover:text-white transition-colors">Cookie Policy</Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
