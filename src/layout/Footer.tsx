import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Mail, Phone, MapPin, Linkedin, Twitter, Youtube } from 'lucide-react';

const Footer = () => {
  const { t } = useTranslation();

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center">
              <img
                src="/images/houbeiLOGO.svg"
                alt="BPMAX Logo"
                className="h-8 w-auto filter brightness-0 invert"
              />
            </div>
            <p className="text-gray-300 text-sm">
              {t('footer.description')}
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Linkedin className="w-5 h-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Twitter className="w-5 h-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Youtube className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Products */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{t('footer.products')}</h3>
            <ul className="space-y-2 text-sm">
              <li><Link to="/products#ai-engine" className="text-gray-300 hover:text-white transition-colors">{t('products.aiEngine')}</Link></li>
              <li><Link to="/products#form-engine" className="text-gray-300 hover:text-white transition-colors">{t('products.formEngine')}</Link></li>
              <li><Link to="/products#data-engine" className="text-gray-300 hover:text-white transition-colors">{t('products.dataEngine')}</Link></li>
              <li><Link to="/products#org-modeling" className="text-gray-300 hover:text-white transition-colors">{t('products.orgModeling')}</Link></li>
              <li><Link to="/products#process-mining" className="text-gray-300 hover:text-white transition-colors">{t('products.processMining')}</Link></li>
            </ul>
          </div>

          {/* Solutions */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{t('footer.solutions')}</h3>
            <ul className="space-y-2 text-sm">
              <li><Link to="/solutions#retail" className="text-gray-300 hover:text-white transition-colors">{t('industries.retail')}</Link></li>
              <li><Link to="/solutions#manufacturing" className="text-gray-300 hover:text-white transition-colors">{t('industries.manufacturing')}</Link></li>
              <li><Link to="/solutions#financial" className="text-gray-300 hover:text-white transition-colors">{t('industries.financial')}</Link></li>
              <li><Link to="/solutions#real-estate" className="text-gray-300 hover:text-white transition-colors">{t('industries.realEstate')}</Link></li>
              <li><Link to="/solutions#pharma" className="text-gray-300 hover:text-white transition-colors">{t('industries.lifeSciences')}</Link></li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{t('footer.contact')}</h3>
            <ul className="space-y-3 text-sm">
              <li className="flex items-center space-x-2">
                <Phone className="w-4 h-4 text-gray-400" />
                <span className="text-gray-300">{t('footer.phone')}</span>
              </li>
              <li className="flex items-center space-x-2">
                <Mail className="w-4 h-4 text-gray-400" />
                <span className="text-gray-300">{t('footer.email')}</span>
              </li>
              <li className="flex items-start space-x-2">
                <MapPin className="w-4 h-4 text-gray-400 mt-0.5" />
                <span className="text-gray-300" style={{ whiteSpace: 'pre-line' }}>
                  {t('footer.address')}
                </span>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <div className="text-sm text-gray-400">
            {t('footer.copyright')}
          </div>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <Link to="/privacy" className="text-sm text-gray-400 hover:text-white transition-colors">{t('footer.privacyPolicy')}</Link>
            <Link to="/terms" className="text-sm text-gray-400 hover:text-white transition-colors">{t('footer.termsOfService')}</Link>
            <Link to="/cookies" className="text-sm text-gray-400 hover:text-white transition-colors">{t('footer.cookiePolicy')}</Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
