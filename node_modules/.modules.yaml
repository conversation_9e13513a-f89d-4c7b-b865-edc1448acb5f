hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.26.2':
    '@babel/code-frame': private
  '@babel/compat-data@7.26.8':
    '@babel/compat-data': private
  '@babel/core@7.26.10':
    '@babel/core': private
  '@babel/generator@7.27.0':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.0':
    '@babel/helper-compilation-targets': private
  '@babel/helper-module-imports@7.25.9':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.26.0(@babel/core@7.26.10)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.26.5':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.25.9':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.25.9':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.25.9':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.0':
    '@babel/helpers': private
  '@babel/parser@7.27.0':
    '@babel/parser': private
  '@babel/plugin-transform-react-jsx-self@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/runtime@7.27.0':
    '@babel/runtime': private
  '@babel/template@7.27.0':
    '@babel/template': private
  '@babel/traverse@7.27.0':
    '@babel/traverse': private
  '@babel/types@7.27.0':
    '@babel/types': private
  '@esbuild/aix-ppc64@0.25.2':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.2':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.2':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.2':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.2':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.2':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.2':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.2':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.2':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.2':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.2':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.2':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.2':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.2':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.2':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.2':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.2':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.2':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.2':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.2':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.2':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.2':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.2':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.2':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.2':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.5.1(eslint@9.24.0(jiti@1.21.7))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.20.0':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.2.1':
    '@eslint/config-helpers': private
  '@eslint/core@0.12.0':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.2.8':
    '@eslint/plugin-kit': private
  '@floating-ui/core@1.6.9':
    '@floating-ui/core': private
  '@floating-ui/dom@1.6.13':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@floating-ui/react-dom': private
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.2':
    '@humanwhocodes/retry': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@radix-ui/number@1.1.1':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.2':
    '@radix-ui/primitive': private
  '@radix-ui/react-arrow@1.1.3(@types/react-dom@18.3.6(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-collection@1.1.3(@types/react-dom@18.3.6(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.2(@types/react@18.3.20)(react@18.3.1)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.2(@types/react@18.3.20)(react@18.3.1)':
    '@radix-ui/react-context': private
  '@radix-ui/react-direction@1.1.1(@types/react@18.3.20)(react@18.3.1)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.6(@types/react-dom@18.3.6(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-focus-guards@1.1.2(@types/react@18.3.20)(react@18.3.1)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.3(@types/react-dom@18.3.6(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-id@1.1.1(@types/react@18.3.20)(react@18.3.1)':
    '@radix-ui/react-id': private
  '@radix-ui/react-menu@2.1.7(@types/react-dom@18.3.6(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-popper@1.2.3(@types/react-dom@18.3.6(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.5(@types/react-dom@18.3.6(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.3(@types/react-dom@18.3.6(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.0.3(@types/react-dom@18.3.6(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-roving-focus@1.1.3(@types/react-dom@18.3.6(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@18.3.20)(react@18.3.1)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.1.1(@types/react@18.3.20)(react@18.3.1)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@18.3.20)(react@18.3.1)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@18.3.20)(react@18.3.1)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.1(@types/react@18.3.20)(react@18.3.1)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.1(@types/react@18.3.20)(react@18.3.1)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.1(@types/react@18.3.20)(react@18.3.1)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.1.3(@types/react-dom@18.3.6(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.1':
    '@radix-ui/rect': private
  '@remix-run/router@1.23.0':
    '@remix-run/router': private
  '@rollup/rollup-android-arm-eabi@4.39.0':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.39.0':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.39.0':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.39.0':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.39.0':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.39.0':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.39.0':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.39.0':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.39.0':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.39.0':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.39.0':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.39.0':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.39.0':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.39.0':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.39.0':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.39.0':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.39.0':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.39.0':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.39.0':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.39.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/d3-array@3.2.1':
    '@types/d3-array': private
  '@types/d3-color@3.1.3':
    '@types/d3-color': private
  '@types/d3-ease@3.0.2':
    '@types/d3-ease': private
  '@types/d3-interpolate@3.0.4':
    '@types/d3-interpolate': private
  '@types/d3-path@3.1.1':
    '@types/d3-path': private
  '@types/d3-scale@4.0.9':
    '@types/d3-scale': private
  '@types/d3-shape@3.1.7':
    '@types/d3-shape': private
  '@types/d3-time@3.0.4':
    '@types/d3-time': private
  '@types/d3-timer@3.0.2':
    '@types/d3-timer': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/history@4.7.11':
    '@types/history': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/prop-types@15.7.14':
    '@types/prop-types': private
  '@types/react-router@5.1.20':
    '@types/react-router': private
  '@typescript-eslint/eslint-plugin@8.29.1(@typescript-eslint/parser@8.29.1(eslint@9.24.0(jiti@1.21.7))(typescript@5.6.3))(eslint@9.24.0(jiti@1.21.7))(typescript@5.6.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.29.1(eslint@9.24.0(jiti@1.21.7))(typescript@5.6.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/scope-manager@8.29.1':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@8.29.1(eslint@9.24.0(jiti@1.21.7))(typescript@5.6.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.29.1':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.29.1(typescript@5.6.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.29.1(eslint@9.24.0(jiti@1.21.7))(typescript@5.6.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.29.1':
    '@typescript-eslint/visitor-keys': private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn@8.14.1:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.4:
    aria-hidden: private
  balanced-match@1.0.2:
    balanced-match: private
  binary-extensions@2.3.0:
    binary-extensions: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.24.4:
    browserslist: private
  callsites@3.1.0:
    callsites: private
  camelcase-css@2.0.1:
    camelcase-css: private
  caniuse-lite@1.0.30001713:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  chokidar@3.6.0:
    chokidar: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  commander@4.1.1:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cross-spawn@7.0.6:
    cross-spawn: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  d3-array@3.2.4:
    d3-array: private
  d3-color@3.1.0:
    d3-color: private
  d3-ease@3.0.1:
    d3-ease: private
  d3-format@3.1.0:
    d3-format: private
  d3-interpolate@3.0.1:
    d3-interpolate: private
  d3-path@3.1.0:
    d3-path: private
  d3-scale@4.0.2:
    d3-scale: private
  d3-shape@3.2.0:
    d3-shape: private
  d3-time-format@4.1.0:
    d3-time-format: private
  d3-time@3.1.0:
    d3-time: private
  d3-timer@3.0.1:
    d3-timer: private
  debug@4.4.0:
    debug: private
  decimal.js-light@2.5.1:
    decimal.js-light: private
  deep-is@0.1.4:
    deep-is: private
  detect-node-es@1.1.0:
    detect-node-es: private
  didyoumean@1.2.2:
    didyoumean: private
  dlv@1.1.3:
    dlv: private
  dom-helpers@5.2.1:
    dom-helpers: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  electron-to-chromium@1.5.136:
    electron-to-chromium: private
  embla-carousel-reactive-utils@8.6.0(embla-carousel@8.6.0):
    embla-carousel-reactive-utils: private
  embla-carousel@8.6.0:
    embla-carousel: private
  emoji-regex@8.0.0:
    emoji-regex: private
  esbuild@0.25.2:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@8.3.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.0:
    eslint-visitor-keys: private
  espree@10.3.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  eventemitter3@4.0.7:
    eventemitter3: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-equals@5.2.2:
    fast-equals: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  foreground-child@3.3.1:
    foreground-child: private
  fraction.js@4.3.7:
    fraction.js: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-nonce@1.0.1:
    get-nonce: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@10.4.5:
    glob: private
  graphemer@1.4.0:
    graphemer: private
  has-flag@4.0.0:
    has-flag: private
  hasown@2.0.2:
    hasown: private
  html-parse-stringify@3.0.1:
    html-parse-stringify: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  internmap@2.0.3:
    internmap: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-core-module@2.16.1:
    is-core-module: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  isexe@2.0.0:
    isexe: private
  jackspeak@3.4.3:
    jackspeak: private
  jiti@1.21.7:
    jiti: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  keyv@4.5.4:
    keyv: private
  levn@0.4.1:
    levn: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  locate-path@6.0.0:
    locate-path: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash@4.17.21:
    lodash: private
  loose-envify@1.4.0:
    loose-envify: private
  lru-cache@5.1.1:
    lru-cache: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  minimatch@3.1.2:
    minimatch: private
  minipass@7.1.2:
    minipass: private
  ms@2.1.3:
    ms: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parent-module@1.0.1:
    parent-module: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pify@2.3.0:
    pify: private
  pirates@4.0.7:
    pirates: private
  postcss-import@15.1.0(postcss@8.4.49):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.4.49):
    postcss-js: private
  postcss-load-config@4.0.2(postcss@8.4.49):
    postcss-load-config: private
  postcss-nested@6.2.0(postcss@8.4.49):
    postcss-nested: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prop-types@15.8.1:
    prop-types: private
  punycode@2.3.1:
    punycode: private
  queue-microtask@1.2.3:
    queue-microtask: private
  react-is@18.3.1:
    react-is: private
  react-refresh@0.14.2:
    react-refresh: private
  react-remove-scroll-bar@2.3.8(@types/react@18.3.20)(react@18.3.1):
    react-remove-scroll-bar: private
  react-remove-scroll@2.6.3(@types/react@18.3.20)(react@18.3.1):
    react-remove-scroll: private
  react-router@6.30.0(react@18.3.1):
    react-router: private
  react-smooth@4.0.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-smooth: private
  react-style-singleton@2.2.3(@types/react@18.3.20)(react@18.3.1):
    react-style-singleton: private
  react-transition-group@4.4.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-transition-group: private
  read-cache@1.0.0:
    read-cache: private
  readdirp@3.6.0:
    readdirp: private
  recharts-scale@0.4.5:
    recharts-scale: private
  regenerator-runtime@0.14.1:
    regenerator-runtime: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  rollup@4.39.0:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  scheduler@0.23.2:
    scheduler: private
  semver@6.3.1:
    semver: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  signal-exit@4.1.0:
    signal-exit: private
  source-map-js@1.2.1:
    source-map-js: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@5.1.2:
    string-width: private
  strip-ansi@6.0.1:
    strip-ansi-cjs: private
  strip-ansi@7.1.0:
    strip-ansi: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  to-regex-range@5.0.1:
    to-regex-range: private
  ts-api-utils@2.1.0(typescript@5.6.3):
    ts-api-utils: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  tslib@2.8.1:
    tslib: private
  type-check@0.4.0:
    type-check: private
  undici-types@6.21.0:
    undici-types: private
  update-browserslist-db@1.1.3(browserslist@4.24.4):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  use-callback-ref@1.3.3(@types/react@18.3.20)(react@18.3.1):
    use-callback-ref: private
  use-sidecar@1.1.3(@types/react@18.3.20)(react@18.3.1):
    use-sidecar: private
  util-deprecate@1.0.2:
    util-deprecate: private
  victory-vendor@36.9.2:
    victory-vendor: private
  void-elements@3.1.0:
    void-elements: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
  yallist@3.1.1:
    yallist: private
  yaml@2.7.1:
    yaml: private
  yocto-queue@0.1.0:
    yocto-queue: private
ignoredBuilds:
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.6.1
pendingBuilds: []
prunedAt: Thu, 19 Jun 2025 06:32:29 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmmirror.com/
skipped:
  - '@esbuild/aix-ppc64@0.25.2'
  - '@esbuild/android-arm64@0.25.2'
  - '@esbuild/android-arm@0.25.2'
  - '@esbuild/android-x64@0.25.2'
  - '@esbuild/darwin-x64@0.25.2'
  - '@esbuild/freebsd-arm64@0.25.2'
  - '@esbuild/freebsd-x64@0.25.2'
  - '@esbuild/linux-arm64@0.25.2'
  - '@esbuild/linux-arm@0.25.2'
  - '@esbuild/linux-ia32@0.25.2'
  - '@esbuild/linux-loong64@0.25.2'
  - '@esbuild/linux-mips64el@0.25.2'
  - '@esbuild/linux-ppc64@0.25.2'
  - '@esbuild/linux-riscv64@0.25.2'
  - '@esbuild/linux-s390x@0.25.2'
  - '@esbuild/linux-x64@0.25.2'
  - '@esbuild/netbsd-arm64@0.25.2'
  - '@esbuild/netbsd-x64@0.25.2'
  - '@esbuild/openbsd-arm64@0.25.2'
  - '@esbuild/openbsd-x64@0.25.2'
  - '@esbuild/sunos-x64@0.25.2'
  - '@esbuild/win32-arm64@0.25.2'
  - '@esbuild/win32-ia32@0.25.2'
  - '@esbuild/win32-x64@0.25.2'
  - '@rollup/rollup-android-arm-eabi@4.39.0'
  - '@rollup/rollup-android-arm64@4.39.0'
  - '@rollup/rollup-darwin-x64@4.39.0'
  - '@rollup/rollup-freebsd-arm64@4.39.0'
  - '@rollup/rollup-freebsd-x64@4.39.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.39.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.39.0'
  - '@rollup/rollup-linux-arm64-gnu@4.39.0'
  - '@rollup/rollup-linux-arm64-musl@4.39.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.39.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.39.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.39.0'
  - '@rollup/rollup-linux-riscv64-musl@4.39.0'
  - '@rollup/rollup-linux-s390x-gnu@4.39.0'
  - '@rollup/rollup-linux-x64-gnu@4.39.0'
  - '@rollup/rollup-linux-x64-musl@4.39.0'
  - '@rollup/rollup-win32-arm64-msvc@4.39.0'
  - '@rollup/rollup-win32-ia32-msvc@4.39.0'
  - '@rollup/rollup-win32-x64-msvc@4.39.0'
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
